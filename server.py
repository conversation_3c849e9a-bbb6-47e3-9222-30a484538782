import http.server
import socketserver
import os

PORT = 8001

# 切换到脚本所在目录，以确保能正确找到 profile.html
web_dir = os.path.dirname(__file__)
if web_dir:
    os.chdir(web_dir)

Handler = http.server.SimpleHTTPRequestHandler

with socketserver.TCPServer(("", PORT), Handler) as httpd:
    print(f"服务器已启动，正在 http://localhost:{PORT} 监听")
    print("您可以在浏览器中打开 http://localhost:8001/profile.html 查看页面")
    httpd.serve_forever()
