# Project Context for AI Assistant (<PERSON><PERSON><PERSON><PERSON>)

This document provides context for the AI assistant about the current project directory structure, its purpose, key files, and usage. This context is intended to guide the AI in future interactions, particularly when modifying files or creating new content.

## Project Type and Overview

This directory (`D:\WorkJobs\Cursor\T1`) serves as a **project workspace** for creating and showcasing various small front-end web development examples, primarily involving JavaScript libraries like P5.js, Three.js, and D3.js. It also contains utilities and instructions for local development and interaction with AI assistants.

The core structure is organized around different technology showcases, with a focus on simplicity and quick iteration.

### Key Directories

*   `CLJsShow`: This is the main directory for JavaScript library demonstrations.
    *   `CLJsShow/p5js`: Contains an interactive P5.js canvas example.
    *   `CLJsShow/threejs`: Contains a 3D rotating cube example using Three.js.
    *   `CLJsShow/d3js`: Contains a bar chart example using D3.js.
*   `Augment`: Appears to be a placeholder or a directory for future augmented content.
*   `.claude`: Contains settings for the Claude AI assistant integration.
*   Other directories (`GeJsShow`, `GPT-5`, `JsShow`, `QWJsShow`) are present but currently empty or placeholders.

### Key Files

*   `profile.html`: A static HTML page representing a user profile.
*   `server.py`: A simple Python script to start a local web server for previewing HTML files.
*   `todo.md`: A task management and review log file. It contains specific workflows and instructions for AI assistants, including the current one (Qwen/Gemini). It's a crucial file for understanding the iterative task process.
*   `CLAUDE.md`, `CLAUDE.local.md`: Specific instructions and workflows for interacting with the Claude AI assistant. `CLAUDE.local.md` contains local overrides, notably specifying the use of traditional `<script>` tags for Three.js.
*   `avatar.svg`: An SVG image file, used as the profile picture in `profile.html`.
*   `claude_help.txt`: Likely contains help text or tips for using the Claude integration.

## Development Workflow

The standard workflow, as outlined in `CLAUDE.md` and `todo.md`, is iterative and review-based:

1.  **Task Analysis:** The AI (or user) identifies a task.
2.  **Planning:** A plan is written to `todo.md`, detailing the steps.
3.  **Execution & Review:** The AI executes the steps, checking them off in `todo.md` as it goes.
4.  **Final Review:** A summary of changes is added to the "Review" section in `todo.md`.

### Running/Testing the Project

The primary way to view HTML content (like `profile.html` or the examples in `CLJsShow`) is by using the built-in Python web server.

*   **Start the Server:** Run `python server.py` in the terminal. This starts a server on `http://localhost:8001`.
*   **View Pages:**
    *   Profile Page: `http://localhost:8001/profile.html`
    *   P5.js Demo: `http://localhost:8001/CLJsShow/p5js/index.html`
    *   Three.js Demo: `http://localhost:8001/CLJsShow/threejs/index.html`
    *   D3.js Demo: `http://localhost:8001/CLJsShow/d3js/index.html`

## Development Conventions

Based on the existing files and instructions:

1.  **AI Task Management:** Use `todo.md` for planning, tracking, and reviewing all AI-assisted tasks.
2.  **JavaScript Libraries:** Prefer loading libraries from a CDN (e.g., jsDelivr, unpkg) directly in `index.html` files.
3.   **Three.js Import Style:** Adhere to the convention specified in `CLAUDE.local.md`: Use traditional `<script src="..."></script>` tags for Three.js instead of ES Modules (`import`) to ensure compatibility.
    ```html
    <!-- Correct -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>

    <!-- Avoid (unless explicitly instructed otherwise) -->
    import * as THREE from 'three';
    ```
4.  **File Structure for Demos:** Each JavaScript library demo resides in its own subdirectory within `CLJsShow` (e.g., `CLJsShow/libraryname/`). This directory typically contains an `index.html` and a separate `.js` file for the example code (e.g., `sketch.js`, `main.js`, `chart.js`).
5.  **Simplicity:** Favor small, focused changes over complex modifications. Keep examples concise and easy to understand.

By understanding this context, the AI assistant should be able to effectively contribute to this workspace, adhering to established conventions and workflows.