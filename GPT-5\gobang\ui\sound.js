export class Sound {
  constructor() {
    this.enabled = true;
    this.ctx = null;
    document.addEventListener('click', () => {
      if (!this.ctx) this.ctx = new (window.AudioContext || window.webkitAudioContext)();
    }, { once: true });
  }
  place() { if (!this._ready()) return; this._beep(440, 0.05, 0.02); }
  win() { if (!this._ready()) return; this._beep(600, 0.12, 0.035); this._beep(820, 0.16, 0.05, 0.05); }
  _ready() { return this.enabled && this.ctx; }
  _beep(freq, duration, gain = 0.03, delay = 0) {
    const t0 = this.ctx.currentTime + delay;
    const osc = this.ctx.createOscillator();
    const g = this.ctx.createGain();
    osc.type = 'sine'; osc.frequency.value = freq;
    g.gain.setValueAtTime(gain, t0);
    g.gain.exponentialRampToValueAtTime(0.0001, t0 + duration);
    osc.connect(g).connect(this.ctx.destination);
    osc.start(t0); osc.stop(t0 + duration + 0.02);
  }
}
