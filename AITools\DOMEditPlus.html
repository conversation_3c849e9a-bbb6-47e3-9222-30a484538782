<!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>HTML/Markdown/SVG/Mermaid 所见即所得编辑器</title>
        <style>
            body {
                margin: 0;
                padding: 0;
                overflow: auto;
                font-family: Arial, sans-serif;
                background-color: #ffffff;
                transition: background-color 0.3s, color 0.3s;
            }

            /* 暗色主题样式 */
            body.dark-theme {
                background-color: #1a1a1a;
                color: #e6e6e6;
            }

            body.dark-theme #container {
                background-color: #1a1a1a;
            }

            body.dark-theme #editor {
                background-color: #1a1a1a;
            }

            body.dark-theme #preview {
                background-color: #2a2a2a;
                border-left-color: #444;
            }

            body.dark-theme #codeTextarea {
                background-color: #2d2d2d;
                color: #e6e6e6;
                border: 1px solid #444;
            }

            body.dark-theme #previewFrame,
            body.dark-theme #svgContainer {
                background-color: #2a2a2a;
            }

            body.dark-theme #downloadToolbar {
                background: rgba(42, 42, 42, 0.95);
                border-top-color: #444;
            }

            body.dark-theme .download-buttons button {
                background: #3a3a3a;
                color: #e6e6e6;
                border: 1px solid #555;
            }

            body.dark-theme .download-buttons button:hover {
                background: #4a4a4a;
                border-color: #ff7a18;
            }

            body.dark-theme #modeMenu {
                background: rgba(42, 42, 42, 0.95);
                border-top-color: #444;
            }

            body.dark-theme #modeSwitch,
            body.dark-theme #openFile,
            body.dark-theme #exampleBtn,
            body.dark-theme #helpBtn,
            body.dark-theme #themeToggle {
                background: #3a3a3a;
                color: #e6e6e6;
                border: 1px solid #555;
            }

            body.dark-theme #modeSwitch:hover,
            body.dark-theme #openFile:hover,
            body.dark-theme #exampleBtn:hover,
            body.dark-theme #helpBtn:hover,
            body.dark-theme #themeToggle:hover {
                background: #4a4a4a;
                border-color: #ff7a18;
            }

            body.dark-theme #divider {
                background-color: #444;
            }

            body.dark-theme #divider:hover {
                background-color: #ff7a18;
            }

            body.dark-theme #notification {
                background-color: #4CAF50;
                color: white;
            }

            body.dark-theme #fullscreenContainer {
                background: #1a1a1a;
            }
            #container {
                display: flex;
                height: 100vh;
                position: relative;
                background-color: #ffffff;
                transition: background-color 0.3s;
            }

            body.dark-theme #container {
                background-color: #1a1a1a;
            }

            #editor, #preview {
                padding: 12px;
                box-sizing: border-box;
                overflow: hidden;
                min-width: 100px;
                height: 100%;
                border: 1px solid #e0e0e0;
                background-color: #ffffff;
                transition: background-color 0.3s, border-color 0.3s;
            }

            body.dark-theme #editor,
            body.dark-theme #preview {
                border-color: #333;
                background-color: #1a1a1a;
            }

            #editor {
                width: calc(50% - 5px);
                position: relative;
                border-right: none;
                border-top-left-radius: 8px;
                border-bottom-left-radius: 8px;
            }

            #preview {
                width: calc(50% - 5px);
                border-left: none;
                background-color: #f8f9fa;
                display: flex;
                flex-direction: column;
                overflow: hidden;
                border-top-right-radius: 8px;
                border-bottom-right-radius: 8px;
            }

            body.dark-theme #preview {
                background-color: #1f1f1f;
            }
            #previewFrame, #svgContainer {
                width: 100%;
                height: 100%;
                border: none;
                background-color: white;
                overflow: auto;
                border-radius: 6px;
                transition: background-color 0.3s;
            }

            body.dark-theme #previewFrame,
            body.dark-theme #svgContainer {
                background-color: #2a2a2a;
            }

            #svgContainer {
                width: 100%;
                height: 100%;
                padding: 16px;
                box-sizing: border-box;
                display: flex;
                justify-content: center;
                align-items: center;
                overflow: auto;
                background-color: white;
            }
            #svgContainer svg {
                max-width: 90%;
                max-height: 90%;
                width: auto !important;
                height: auto !important;
                margin: auto;
            }
            #svgContainer p {
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 100%;
                font-family: Arial, sans-serif;
                color: #666;
                text-align: center;
            }
            /* 删除重复的 #svgContainer p 简化样式 */
            /* 去除重复冲突的 svg 样式，统一为最大 90% 自适应 */
            #divider {
                width: 10px;
                cursor: col-resize;
                background-color: #e0e0e0;
                flex-shrink: 0;
                position: relative;
                transition: background-color 0.3s;
                border-top: 1px solid #e0e0e0;
                border-bottom: 1px solid #e0e0e0;
            }

            #divider:hover {
                background-color: #ff7a18;
            }

            #divider::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 3px;
                height: 20px;
                background: repeating-linear-gradient(
                    to bottom,
                    #999 0px,
                    #999 2px,
                    transparent 2px,
                    transparent 4px
                );
                opacity: 0.6;
                transition: opacity 0.3s;
            }

            #divider:hover::before {
                opacity: 1;
                background: repeating-linear-gradient(
                    to bottom,
                    white 0px,
                    white 2px,
                    transparent 2px,
                    transparent 4px
                );
            }

            body.dark-theme #divider {
                background-color: #333;
                border-color: #333;
            }

            body.dark-theme #divider:hover {
                background-color: #ff7a18;
            }

            body.dark-theme #divider::before {
                background: repeating-linear-gradient(
                    to bottom,
                    #666 0px,
                    #666 2px,
                    transparent 2px,
                    transparent 4px
                );
            }
            textarea {
                width: 100%;
                height: 100%;
                resize: none;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 14px;
                line-height: 1.5;
                box-sizing: border-box;
                border: none;
                outline: none;
                background: transparent;
                color: #333;
                padding: 0;
                transition: color 0.3s;
            }

            body.dark-theme textarea {
                color: #e6e6e6;
            }

            textarea::placeholder {
                color: #999;
                font-style: italic;
            }

            body.dark-theme textarea::placeholder {
                color: #666;
            }
            /* 下载工具区域：默认隐藏，悬停/聚焦显示 */
            #downloadToolbar {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1001;
            }
            #downloadToolbar .hover-hit {
                width: 36px;
                height: 36px;
                background: transparent;
            }
            .download-buttons {
                display: flex;
                flex-direction: column;
                gap: 10px;
                opacity: 0;
                transform: translateY(6px);
                pointer-events: none;
                transition: opacity 0.25s ease, transform 0.25s ease;
            }
            #downloadToolbar:hover .download-buttons,
            #downloadToolbar:focus-within .download-buttons {
                opacity: 1;
                transform: translateY(0);
                pointer-events: auto;
            }
            .download-buttons button {
                padding: 10px 15px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                transition: background-color 0.3s;
            }
            .download-buttons button:hover {
                background-color: #45a049;
            }
            #notification {
                position: fixed;
                bottom: 20px;
                left: 20px;
                background-color: #4CAF50;
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                display: none;
                opacity: 0;
                transition: opacity 0.3s, transform 0.3s;
                transform: translateY(10px);
                z-index: 10003;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                word-wrap: break-word;
            }

            #notification.show {
                opacity: 1;
                transform: translateY(0);
            }
            /* 模式切换区域：默认隐藏，悬停/聚焦显示 */
            #modeMenu {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1001;
            }
            #modeMenu .hover-hit {
                width: 36px;
                height: 36px;
                background: transparent;
            }
            #modeMenu .menu-controls {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                opacity: 0;
                transform: translateY(-6px);
                pointer-events: none;
                transition: opacity 0.25s ease, transform 0.25s ease;
            }

            #openFile, #exampleBtn, #helpBtn {
                background: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                padding: 6px 8px;
                transition: background-color 0.3s, border-color 0.3s;
            }

            #openFile:hover, #exampleBtn:hover, #helpBtn:hover {
                background: #e0e0e0;
                border-color: #ff7a18;
            }
            #modeMenu:hover .menu-controls,
            #modeMenu:focus-within .menu-controls {
                opacity: 1;
                transform: translateY(0);
                pointer-events: auto;
            }

            /* 触屏设备直接显示控件 */
            @media (hover: none) {
                #downloadToolbar .download-buttons { opacity: 1; transform: none; pointer-events: auto; }
                #modeMenu .menu-controls { opacity: 1; transform: none; pointer-events: auto; }
            }
            #gifProgress {
                position: fixed;
                bottom: 20px;
                left: 20px;
                background-color: rgba(255, 255, 255, 0.8);
                padding: 10px;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            }
            #gifProgressBar {
                width: 200px;
                height: 20px;
            }
            #gifProgressText {
                margin-left: 10px;
            }
            .mermaid {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 100%;
                background: white;
                margin: 0;
                padding: 0;
            }

            .mermaid svg {
                max-width: 90%;
                max-height: 90%;
                width: auto !important;
                height: auto !important;
                margin: auto;
            }

            /* 全屏预览样式 */
            #fullscreenContainer {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: white;
                z-index: 10000;
                display: flex;
                flex-direction: column;
            }

            #fullscreenToolbar {
                position: absolute;
                top: 20px;
                right: 20px;
                z-index: 10001;
            }

            #exitFullscreen {
                background: rgba(0, 0, 0, 0.8);
                color: white;
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                font-size: 18px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.3s;
            }

            #exitFullscreen:hover {
                background: rgba(0, 0, 0, 1);
            }

            #fullscreenContent {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                overflow: auto;
                padding: 20px;
                box-sizing: border-box;
            }

            #fullscreenFrame {
                width: 100%;
                height: 100%;
                border: none;
                background: white;
            }

            #fullscreenSvg {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                background: white;
            }

            body.dark-theme #fullscreenFrame {
                background: #2a2a2a;
            }

            body.dark-theme #fullscreenSvg {
                background: #2a2a2a;
            }

            #fullscreenSvg svg {
                max-width: 95%;
                max-height: 95%;
                width: auto !important;
                height: auto !important;
            }

            /* 拖拽文件样式 */
            .drag-over {
                background-color: rgba(255, 122, 24, 0.1) !important;
                border: 2px dashed #ff7a18 !important;
                border-radius: 8px !important;
                box-shadow: 0 0 20px rgba(255, 122, 24, 0.2) !important;
            }

            body.dark-theme .drag-over {
                background-color: rgba(255, 122, 24, 0.15) !important;
                box-shadow: 0 0 20px rgba(255, 122, 24, 0.3) !important;
            }

            /* 导出主题选择菜单 */
            #exportThemeMenu {
                position: fixed;
                background: white;
                border: 1px solid #ccc;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                padding: 12px;
                min-width: 180px;
                z-index: 10001;
                display: none;
            }

            body.dark-theme #exportThemeMenu {
                background: #2a2a2a;
                border-color: #444;
                color: #e6e6e6;
            }

            .export-theme-option {
                padding: 8px 12px;
                cursor: pointer;
                border-radius: 4px;
                margin: 2px 0;
                font-size: 13px;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .export-theme-option:hover {
                background: #f5f5f5;
            }

            body.dark-theme .export-theme-option:hover {
                background: #3a3a3a;
            }

            .export-theme-option.active {
                background: #fff3e6;
                color: #ff7a18;
                font-weight: 500;
            }

            body.dark-theme .export-theme-option.active {
                background: #33261a;
            }

            /* 帮助对话框样式 */
            #helpDialog {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.5);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 10002;
            }

            .help-content {
                background: white;
                border-radius: 12px;
                padding: 24px;
                max-width: 500px;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
                position: relative;
            }

            body.dark-theme .help-content {
                background: #2a2a2a;
                color: #e6e6e6;
            }

            .help-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 12px;
                border-bottom: 2px solid #ff7a18;
            }

            .help-title {
                font-size: 20px;
                font-weight: bold;
                color: #ff7a18;
                margin: 0;
            }

            .help-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #666;
                padding: 4px;
                border-radius: 4px;
                transition: background-color 0.2s;
            }

            .help-close:hover {
                background: #f0f0f0;
            }

            body.dark-theme .help-close {
                color: #ccc;
            }

            body.dark-theme .help-close:hover {
                background: #3a3a3a;
            }

            .shortcut-section {
                margin-bottom: 20px;
            }

            .shortcut-section h3 {
                color: #ff7a18;
                font-size: 16px;
                margin: 0 0 12px 0;
                font-weight: 600;
            }

            .shortcut-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #eee;
            }

            body.dark-theme .shortcut-item {
                border-bottom-color: #444;
            }

            .shortcut-item:last-child {
                border-bottom: none;
            }

            .shortcut-desc {
                flex: 1;
                font-size: 14px;
            }

            .shortcut-keys {
                display: flex;
                gap: 4px;
                font-family: monospace;
                font-size: 12px;
            }

            .key {
                background: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 2px 6px;
                font-weight: bold;
                color: #333;
            }

            body.dark-theme .key {
                background: #3a3a3a;
                border-color: #555;
                color: #e6e6e6;
            }
        </style>
    </head>
    <body>
        <div id="container">
            <div id="editor">
                <textarea id="codeTextarea" placeholder="请在编辑器中输入源代码" aria-label="源码编辑器"></textarea>
            </div>
            <div id="divider" role="separator" aria-orientation="vertical" tabindex="0" aria-valuemin="100" aria-valuemax="1000" aria-valuenow="0"></div>
            <div id="preview">
        <iframe id="previewFrame" sandbox="allow-scripts allow-same-origin" title="预览区域"></iframe>
                <div id="svgContainer"></div>
            </div>
        </div>
        <div id="downloadToolbar" aria-label="导出工具" role="region">
            <div class="hover-hit" tabindex="0" aria-hidden="true"></div>
            <div class="download-buttons" role="toolbar" aria-label="导出工具按钮">
                <button id="copyPng" aria-label="复制PNG">复制 PNG</button>
                <button id="copyText" aria-label="复制文本">复制文本</button>
                <button id="copyHtml" aria-label="复制HTML" style="display: none;">复制 HTML</button>
                <button id="downloadCode" aria-label="下载源码">下载源码</button>
                <button id="downloadSvg" aria-label="下载SVG" style="display: none;">下载 SVG</button>
                <button id="downloadPdf" aria-label="下载PDF">下载 PDF</button>
                <button id="downloadPng" aria-label="下载PNG">下载 PNG</button>
                <button id="downloadGif" aria-label="下载GIF">下载 GIF</button>
                <button id="fullscreenPreview" aria-label="全屏预览">全屏预览</button>
                <button id="exportThemeToggle" aria-label="导出主题选择" title="选择导出主题">🎨</button>
            </div>
        </div>
        <div id="gifProgress" style="display: none;">
            <progress id="gifProgressBar" value="0" max="100"></progress>
            <span id="gifProgressText">0%</span>
        </div>
        <div id="modeMenu" aria-label="模式切换" role="region">
            <div class="hover-hit" tabindex="0" aria-hidden="true"></div>
            <div class="menu-controls">
                <select id="modeSwitch" aria-label="模式切换">
                    <option value="auto" selected>自动模式</option>
                    <option value="html">HTML 模式</option>
                    <option value="svg">SVG 模式</option>
                    <option value="mermaid">Mermaid 模式</option>
                    <option value="markdown">Markdown 模式</option>
                </select>
                <select id="mermaidSwitch" aria-label="内嵌 Mermaid 预览">
                    <option value="auto" selected>Mermaid 预览：自动</option>
                    <option value="on">Mermaid 预览：开启</option>
                    <option value="off">Mermaid 预览：关闭</option>
                </select>
                <button id="openFile" title="打开文件" aria-label="打开文件" style="height:36px;">📂</button>
                <button id="exampleBtn" title="插入示例" aria-label="插入示例" style="height:36px;">📝</button>
                <button id="helpBtn" title="快捷键帮助" aria-label="快捷键帮助" style="height:36px;">❓</button>
                <button id="themeToggle" title="切换到暗色主题" aria-label="切换主题" style="height:36px;">🌙</button>
            </div>
        </div>

        <!-- 隐藏的文件输入 -->
        <input type="file" id="fileInput" accept=".md,.svg,.html,.htm,.mmd,.txt" style="display: none;">

        <!-- 全屏预览容器 -->
        <div id="fullscreenContainer" style="display: none;">
            <div id="fullscreenToolbar">
                <button id="exitFullscreen" title="退出全屏 (ESC)">✕</button>
            </div>
            <div id="fullscreenContent">
                <iframe id="fullscreenFrame" sandbox="allow-scripts allow-same-origin"></iframe>
                <div id="fullscreenSvg"></div>
            </div>
        </div>

        <div id="notification" aria-live="polite" role="status">PNG 已复制到剪贴板</div>

        <!-- 帮助对话框 -->
        <div id="helpDialog">
            <div class="help-content">
                <div class="help-header">
                    <h2 class="help-title">快捷键帮助</h2>
                    <button class="help-close" onclick="closeHelpDialog()">✕</button>
                </div>

                <div class="shortcut-section">
                    <h3>文件操作</h3>
                    <div class="shortcut-item">
                        <span class="shortcut-desc">打开文件</span>
                        <div class="shortcut-keys">
                            <span class="key">Ctrl</span><span class="key">O</span>
                        </div>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-desc">下载源码</span>
                        <div class="shortcut-keys">
                            <span class="key">Ctrl</span><span class="key">S</span>
                        </div>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-desc">清空编辑器</span>
                        <div class="shortcut-keys">
                            <span class="key">Ctrl</span><span class="key">L</span>
                        </div>
                    </div>
                </div>

                <div class="shortcut-section">
                    <h3>导出功能</h3>
                    <div class="shortcut-item">
                        <span class="shortcut-desc">下载PNG</span>
                        <div class="shortcut-keys">
                            <span class="key">Ctrl</span><span class="key">Shift</span><span class="key">S</span>
                        </div>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-desc">下载PDF</span>
                        <div class="shortcut-keys">
                            <span class="key">Ctrl</span><span class="key">P</span>
                        </div>
                    </div>
                </div>

                <div class="shortcut-section">
                    <h3>界面操作</h3>
                    <div class="shortcut-item">
                        <span class="shortcut-desc">全屏预览</span>
                        <div class="shortcut-keys">
                            <span class="key">F11</span>
                        </div>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-desc">切换主题</span>
                        <div class="shortcut-keys">
                            <span class="key">Ctrl</span><span class="key">/</span>
                        </div>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-desc">退出全屏</span>
                        <div class="shortcut-keys">
                            <span class="key">ESC</span>
                        </div>
                    </div>
                </div>

                <div class="shortcut-section">
                    <h3>分隔条操作</h3>
                    <div class="shortcut-item">
                        <span class="shortcut-desc">双击复位布局</span>
                        <div class="shortcut-keys">
                            <span class="key">双击分隔条</span>
                        </div>
                    </div>
                    <div class="shortcut-item">
                        <span class="shortcut-desc">键盘调整宽度</span>
                        <div class="shortcut-keys">
                            <span class="key">←</span><span class="key">→</span>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding-top: 16px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center;">
                    <span style="color: #ff7a18;">提示：</span>Mac 用户请将 Ctrl 替换为 Cmd
                </div>

                <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0; font-size: 11px; color: #999; text-align: center;">
                    @fresh程序员
                </div>
            </div>
        </div>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/gifshot/0.4.5/gifshot.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js"></script>
        <script>
            // 先定义函数
            // 动态加载脚本（用于按需加载 Markdown 依赖）
            const __loadedScripts = new Set();
            function loadScriptOnce(url) {
                return new Promise((resolve, reject) => {
                    if (__loadedScripts.has(url)) return resolve();
                    const s = document.createElement('script');
                    s.src = url;
                    s.async = true;
                    s.onload = () => { __loadedScripts.add(url); resolve(); };
                    s.onerror = () => reject(new Error('脚本加载失败: ' + url));
                    document.head.appendChild(s);
                });
            }
            function ensureMarkdownLibs() {
                const libs = [
                    'https://cdn.jsdelivr.net/npm/marked@12.0.2/marked.min.js',
                    'https://cdn.jsdelivr.net/npm/dompurify@3.0.9/dist/purify.min.js',
                    'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js',
                ];
                // 顺序加载，保证依赖可用
                return libs.reduce((p, url) => p.then(() => loadScriptOnce(url)), Promise.resolve());
            }
            function detectCodeType(code) {
                // 更稳健的检测算法：优先解析 SVG；HTML 依据结构标签；Mermaid 依据顶层关键字；Markdown 计分
                const src = (code || '').trim();
                if (!src) return 'unknown';

                const lower = src.toLowerCase();
                const head = src.slice(0, 2000);

                // Markdown 内围栏的 Mermaid 优先视作 Markdown
                if (/```\s*mermaid\b[\s\S]*?```/i.test(src)) {
                    return 'markdown';
                }

                // SVG：基于 XML 解析与根节点判定；兼容 xmlns 与 xml 声明
                const looksLikeSvg = /<\?xml\s+version=/i.test(head) || /<svg\b/i.test(head) || /xmlns\s*=\s*"http:\/\/www\.w3\.org\/2000\/svg"/i.test(head);
                if (looksLikeSvg) {
                    try {
                        const parser = new DOMParser();
                        const svgDoc = parser.parseFromString(src, 'image/svg+xml');
                        const rootEl = svgDoc && svgDoc.documentElement;
                        const parserError = svgDoc.querySelector('parsererror');
                        if (!parserError && rootEl && rootEl.tagName && rootEl.tagName.toLowerCase() === 'svg') {
                            return 'svg';
                        }
                    } catch (_) {}
                }

                // HTML：完整文档或常见标签片段（排除纯 SVG 已在上方处理）
                const looksLikeHtml = (
                    lower.startsWith('<!doctype html>') ||
                    /^\s*<html\b/.test(src) ||
                    /<\s*body\b/i.test(head) ||
                    /<\s*(div|p|span|section|article|header|footer|main|nav|h[1-6]|table|thead|tbody|tr|td|th|ul|ol|li|a|img|canvas|video|audio|form|input|button|pre|code)\b/i.test(head)
                );
                if (looksLikeHtml) {
                    const tagMatches = head.match(/<\s*\/?\s*[a-z][\w:-]*\b[^>]*>/gi) || [];
                    if (tagMatches.length >= 2) return 'html';
                }

                // Mermaid：裸顶层关键字（允许前导注释/空白）
                const mermaidTopLevel = /^\s*(?:%%.*\n\s*)*(graph|flowchart|sequenceDiagram|classDiagram|gantt|pie|stateDiagram(?:-v2)?|journey|erDiagram|gitGraph|mindmap)\b/i.test(src);
                if (mermaidTopLevel) {
                    return 'mermaid';
                }

                // Markdown：基于特征计分
                const lines = src.split(/\r?\n/);
                let score = 0;
                const mdTests = [
                    /^\s{0,3}#{1,6}\s+.+/,            // 标题
                    /^\s{0,3}>\s+.+/,                 // 引用
                    /^\s{0,3}[-*+]\s+.+/,             // 无序列表
                    /^\s{0,3}\d+\.[)\s]+.+/,        // 有序列表
                    /^\s{0,3}```[a-zA-Z0-9_-]*\s*$/,  // 代码围栏开始
                    /\|.*\|/,                         // 表格
                    /\[.+\]\(.+\)/,                 // 链接
                    /!\[[^\]]*\]\([^\)]+\)/       // 图片
                ];
                for (let i = 0; i < Math.min(lines.length, 80); i++) {
                    const l = lines[i];
                    if (mdTests.some(r => r.test(l))) score++;
                    if (score >= 3) return 'markdown';
                }
                if (/^\s*[-*_]{3,}\s*$/.test(lines[0] || '')) return 'markdown';
                if (/^\s*`{3,}/.test(lines[0] || '')) return 'markdown';

                return 'unknown';
            }

            function getShortTimestamp() {
                const now = new Date();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                return `${month}${day}_${hours}${minutes}`;
            }

            function showDefaultMessage() {
                const message = '请在左侧输入有效的 HTML / Markdown / SVG / Mermaid 源码';
                const isDark = currentTheme === 'dark';
                const defaultContent = `
                    <html>
                    <head>
                        <style>
                            body, html {
                                margin: 0;
                                padding: 0;
                                height: 100%;
                                display: flex;
                                flex-direction: column;
                                justify-content: center;
                                align-items: center;
                                font-family: Arial, sans-serif;
                                background: ${isDark ? '#2a2a2a' : 'white'};
                            }
                            .message {
                                color: ${isDark ? '#999' : '#666'};
                                text-align: center;
                                margin-bottom: 20px;
                            }
                            .actions {
                                display: flex;
                                gap: 12px;
                                flex-wrap: wrap;
                                justify-content: center;
                            }
                            .action-btn {
                                background: ${isDark ? '#3a3a3a' : '#f0f0f0'};
                                border: 1px solid ${isDark ? '#555' : '#ccc'};
                                color: ${isDark ? '#e6e6e6' : '#333'};
                                padding: 8px 16px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 13px;
                                text-decoration: none;
                                transition: all 0.2s;
                            }
                            .action-btn:hover {
                                background: ${isDark ? '#4a4a4a' : '#e0e0e0'};
                                border-color: #ff7a18;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="message">${message}</div>
                        <div class="actions">
                            <button class="action-btn" onclick="parent.document.getElementById('exampleBtn').click()">📝 插入示例</button>
                            <button class="action-btn" onclick="parent.document.getElementById('openFile').click()">📂 打开文件</button>
                        </div>
                    </body>
                    </html>
                `;

                if (currentMode === 'html' || currentMode === 'markdown') {
                    previewFrame.style.display = 'block';
                    svgContainer.style.display = 'none';
                    previewFrame.srcdoc = defaultContent;
                } else {
                    previewFrame.style.display = 'none';
                    svgContainer.style.display = 'flex';
                    // 为SVG容器的消息也应用主题颜色
                    svgContainer.innerHTML = `<p style="color: ${isDark ? '#999' : '#666'}; margin: 0; text-align: center;">${message}</p>`;
                }
            }

            function switchMode(mode) {
                currentMode = mode;
                modeSwitch.value = mode;
                if (mode === 'html' || mode === 'markdown') {
                    previewFrame.style.display = 'block';
                    svgContainer.style.display = 'none';
                } else if (mode === 'svg') {
                    previewFrame.style.display = 'none';
                    svgContainer.style.display = 'flex';
                } else if (mode === 'mermaid') {
                    previewFrame.style.display = 'none';
                    svgContainer.style.display = 'flex';
                }

                // 更新按钮显示状态
                updateButtonVisibility(mode);

                if (codeTextarea.value.trim() === '') {
                    showDefaultMessage();
                } else {
                    updatePreview();
                }
                try { localStorage.setItem('domedit:mode', mode); } catch(_) {}
            }

            // 更新按钮显示状态
            function updateButtonVisibility(mode) {
                const downloadSvgBtn = document.getElementById('downloadSvg');
                const copyHtmlBtn = document.getElementById('copyHtml');

                // SVG下载按钮只在SVG和Mermaid模式下显示
                if (mode === 'svg' || mode === 'mermaid') {
                    downloadSvgBtn.style.display = 'block';
                } else {
                    downloadSvgBtn.style.display = 'none';
                }

                // 复制HTML按钮只在Markdown模式下显示
                if (mode === 'markdown') {
                    copyHtmlBtn.style.display = 'block';
                } else {
                    copyHtmlBtn.style.display = 'none';
                }
            }

            function generateFileName(code, mode, timestamp) {
                if (mode === 'mermaid') {
                    code = code.trim().toLowerCase();
                    if (code.includes('graph') || code.includes('flowchart')) {
                        return `flowchart_${timestamp}.mmd`;
                    } else if (code.includes('sequencediagram')) {
                        return `sequence_${timestamp}.mmd`;
                    } else if (code.includes('gantt')) {
                        return `gantt_${timestamp}.mmd`;
                    } else if (code.includes('classdiagram')) {
                        return `class_${timestamp}.mmd`;
                    } else if (code.startsWith('pie') || code.includes('pie title')) {
                        return `pie_${timestamp}.mmd`;
                    } else if (code.includes('statediagram')) {
                        return `state_${timestamp}.mmd`;
                    } else if (code.includes('journey')) {
                        return `journey_${timestamp}.mmd`;
                    } else if (code.includes('erdiagram')) {
                        return `er_${timestamp}.mmd`;
                    } else if (code.includes('gitgraph')) {
                        return `git_${timestamp}.mmd`;
                    } else if (code.includes('mindmap')) {
                        return `mindmap_${timestamp}.mmd`;
                    }
                    return `diagram_${timestamp}.mmd`;
                } else if (mode === 'markdown') {
                    // Markdown 默认保存为 .md
                    // 尝试从首个标题生成名称
                    const m = code.match(/^[\s\n\r]*#{1,6}\s+([^\n\r#].*)/);
                    if (m && m[1]) {
                        return `${slugify(m[1]) || 'note'}_${timestamp}.md`;
                    }
                    return `note_${timestamp}.md`;
                }
                return `diagram_${timestamp}.${mode}`;
            }

            // 示例模板数据
            const exampleTemplates = {
                html: {
                    title: 'HTML 示例',
                    content: `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>示例页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { color: #ff7a18; border-bottom: 2px solid #ff7a18; padding-bottom: 10px; }
        .content { margin: 20px 0; }
        .highlight { background: #fff3e6; padding: 15px; border-left: 4px solid #ff7a18; }
    </style>
</head>
<body>
    <h1 class="header">欢迎使用 HTML 模式</h1>
    <div class="content">
        <p>这是一个简单的 HTML 示例，展示了基本的页面结构和样式。</p>
        <div class="highlight">
            <strong>提示：</strong>您可以在这里编写任何 HTML 代码，包括 CSS 和 JavaScript。
        </div>
    </div>
</body>
</html>`
                },
                markdown: {
                    title: 'Markdown 示例',
                    content: `# Markdown 示例文档

## 基本语法演示

### 文本格式
这是 **粗体文本** 和 *斜体文本*，还有 \`行内代码\`。

### 列表
- 无序列表项 1
- 无序列表项 2
  - 嵌套项目
  - 另一个嵌套项目

1. 有序列表项 1
2. 有序列表项 2

### 代码块
\`\`\`javascript
function hello() {
    console.log("Hello, World!");
}
\`\`\`

### 表格
| 功能 | 描述 | 状态 |
|------|------|------|
| 预览 | 实时预览 | ✅ |
| 导出 | 多格式导出 | ✅ |
| 主题 | 明暗主题 | ✅ |

### 引用
> 这是一个引用块，用于突出显示重要信息或引用他人的话。

---

**提示：** 您可以在 Markdown 中嵌入 Mermaid 图表：

\`\`\`mermaid
graph TD
    A[开始] --> B{判断条件}
    B -->|是| C[执行操作]
    B -->|否| D[结束]
    C --> D
\`\`\``
                },
                svg: {
                    title: 'SVG 示例',
                    content: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff7a18;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e66b00;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect width="400" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

  <!-- 标题 -->
  <text x="200" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333">SVG 示例图形</text>

  <!-- 圆形 -->
  <circle cx="120" cy="120" r="40" fill="url(#grad1)" stroke="#333" stroke-width="2"/>
  <text x="120" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white">圆形</text>

  <!-- 矩形 -->
  <rect x="200" y="80" width="80" height="80" fill="#28a745" stroke="#333" stroke-width="2" rx="10"/>
  <text x="240" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white">矩形</text>

  <!-- 路径 -->
  <path d="M 50 200 Q 200 150 350 200 T 350 250" stroke="#dc3545" stroke-width="3" fill="none"/>
  <text x="200" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">贝塞尔曲线</text>
</svg>`
                },
                mermaid: {
                    title: 'Mermaid 流程图',
                    content: `graph TD
    A[项目开始] --> B{需求分析}
    B -->|明确| C[设计方案]
    B -->|不明确| D[收集更多信息]
    D --> B
    C --> E[开发实现]
    E --> F[测试验证]
    F -->|通过| G[部署上线]
    F -->|失败| H[修复问题]
    H --> F
    G --> I[项目完成]

    style A fill:#e1f5fe
    style I fill:#c8e6c9
    style B fill:#fff3e0
    style F fill:#fce4ec`
                }
            };

            // 然后再暴露到全局
            window.detectCodeType = detectCodeType;
            window.getShortTimestamp = getShortTimestamp;
            window.showDefaultMessage = showDefaultMessage;
            window.switchMode = switchMode;
            window.generateFileName = generateFileName;

            const codeTextarea = document.getElementById('codeTextarea');
            const previewFrame = document.getElementById('previewFrame');
            const svgContainer = document.getElementById('svgContainer');
            const divider = document.getElementById('divider');
            const editor = document.getElementById('editor');
            const preview = document.getElementById('preview');
            const container = document.getElementById('container');
            const notification = document.getElementById('notification');
            const modeSwitch = document.getElementById('modeSwitch');
            const mermaidSwitch = document.getElementById('mermaidSwitch');
            let renderEmbeddedMermaid = (localStorage.getItem('domedit:renderMermaid') || 'auto');
            try { if (!['auto','on','off'].includes(renderEmbeddedMermaid)) renderEmbeddedMermaid = 'auto'; } catch (_) {}
            try { mermaidSwitch.value = renderEmbeddedMermaid; } catch (_) {}

            const themeToggle = document.getElementById('themeToggle');
            const openFileBtn = document.getElementById('openFile');
            const fileInput = document.getElementById('fileInput');
            const exampleBtn = document.getElementById('exampleBtn');
            const helpBtn = document.getElementById('helpBtn');

            let currentMode = 'auto';
            let currentTheme = (localStorage.getItem('domedit:theme') || 'light');
            let exportTheme = 'auto'; // 'auto', 'light', 'dark'
            function setTheme(theme) {
                currentTheme = theme === 'dark' ? 'dark' : 'light';
                try { localStorage.setItem('domedit:theme', currentTheme); } catch(_) {}

                // 切换页面主体主题
                const isDark = currentTheme === 'dark';
                if (isDark) {
                    document.body.classList.add('dark-theme');
                    themeToggle.textContent = '☀️';
                    themeToggle.title = '切换到浅色主题';
                } else {
                    document.body.classList.remove('dark-theme');
                    themeToggle.textContent = '🌙';
                    themeToggle.title = '切换到暗色主题';
                }

                // 重新初始化 mermaid 主题
                if (typeof mermaid !== 'undefined' && mermaid.initialize) {
                    try {
                        mermaid.initialize({
                            startOnLoad: false,
                            securityLevel: 'loose',
                            theme: 'default',
                            themeVariables: {
                                primaryColor: isDark ? '#ff8c3a' : '#ff7a18',
                                primaryTextColor: isDark ? '#e6e6e6' : '#222222',
                                lineColor: isDark ? '#ff8c3a' : '#ff7a18',
                                secondaryColor: isDark ? '#2a2a2a' : '#ffe8d6',
                                tertiaryColor: isDark ? '#1f1f1f' : '#fff3e6',
                                fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif"
                            }
                        });
                    } catch(_) {}
                }
                updatePreview();
            }

            function updatePreview() {
            const code = codeTextarea.value.trim();
                if (!code) {
                    showDefaultMessage();
                    try { modeSwitch.value = currentMode; } catch(_) {}
                    return;
                }
                // 统一决定本次渲染使用的模式：
                // - auto: 使用探测结果
                // - markdown: 若探测到 html/svg/mermaid，优先使用探测结果，避免误判
                // - 其他非 auto: 使用当前模式
                const detectedMode = detectCodeType(code);
                const renderMode = (function(){
                    if (currentMode === 'auto') return detectedMode;
                    if (currentMode === 'markdown' && (detectedMode === 'html' || detectedMode === 'svg' || detectedMode === 'mermaid')) return detectedMode;
                    return currentMode;
                })();
                // 自动模式下，同步下拉框的显示为检测到的模式（避免触发change事件）
                try {
                    const targetValue = currentMode === 'auto'
                        ? ((detectedMode && detectedMode !== 'unknown') ? detectedMode : 'auto')
                        : currentMode;

                    // 只有当值真正不同时才更新，避免不必要的事件触发
                    if (modeSwitch.value !== targetValue) {
                        // 使用标志位避免在change事件中执行switchMode
                        window._isUpdatingModeDisplay = true;
                        modeSwitch.value = targetValue;
                        setTimeout(() => { window._isUpdatingModeDisplay = false; }, 0);
                    }
                } catch (_) {}

                // 根据renderMode切换显示容器
                if (renderMode === 'html' || renderMode === 'markdown') {
                    previewFrame.style.display = 'block';
                    svgContainer.style.display = 'none';
                } else if (renderMode === 'svg' || renderMode === 'mermaid') {
                    previewFrame.style.display = 'none';
                    svgContainer.style.display = 'flex';
                }

                // 更新按钮显示状态
                updateButtonVisibility(renderMode);

                if (renderMode === 'mermaid') {
                    try {
                        svgContainer.innerHTML = '<pre class="mermaid"></pre>';
                        const mermaidDiv = svgContainer.querySelector('.mermaid');
                        mermaidDiv.textContent = code;

                        const uniqueId = 'mermaid-' + Date.now();
                        mermaidDiv.id = uniqueId;

                        window.mermaidRendered = false;

                        if (!window.__mermaidInited && typeof mermaid !== 'undefined' && mermaid.initialize) {
                            try {
                                mermaid.initialize({
                                    startOnLoad: false,
                                    securityLevel: 'loose',
                                    theme: 'default',
                                    themeVariables: {
                                        primaryColor: '#ff7a18',
                                        primaryTextColor: '#222222',
                                        lineColor: '#ff7a18',
                                        secondaryColor: '#ffe8d6',
                                        tertiaryColor: '#fff3e6',
                                        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif"
                                    }
                                });
                                window.__mermaidInited = true;
                            } catch(_) {}
                        }

                        mermaid.render(uniqueId, code).then(({svg}) => {
                            svgContainer.innerHTML = svg;
                            window.mermaidRendered = true;
                        }).catch(error => {
                            console.error('Mermaid 渲染错误:', error);
                            svgContainer.innerHTML = `<p style="color: red; text-align: center;">Mermaid 渲染错误: ${error.message}</p>`;
                        });
                    } catch (error) {
                        console.error('Mermaid 解析错误:', error);
                        svgContainer.innerHTML = `<p style="color: red; text-align: center;">Mermaid 解析错误: ${error.message}</p>`;
                    }
                } else if (renderMode === 'svg') {
                    try {
                        const parser = new DOMParser();
                        const svgDoc = parser.parseFromString(code, 'image/svg+xml');
                        const parserError = svgDoc.querySelector('parsererror');
                        if (parserError) {
                            throw new Error(parserError.textContent);
                        }
                        const svgElement = svgDoc.documentElement;
                        sanitizeSvgElement(svgElement);

                        if (svgElement.tagName.toLowerCase() !== 'svg') {
                            throw new Error('根元素不是 <svg>');
                        }

                        if (!svgElement.getAttribute('viewBox')) {
                            const wAttr = svgElement.getAttribute('width');
                            const hAttr = svgElement.getAttribute('height');
                            const w = (wAttr && !/%/.test(wAttr)) ? parseFloat(wAttr) : 800;
                            const h = (hAttr && !/%/.test(hAttr)) ? parseFloat(hAttr) : 600;
                            if (w && h) svgElement.setAttribute('viewBox', `0 0 ${w} ${h}`);
                        }

                        svgElement.removeAttribute('width');
                        svgElement.removeAttribute('height');
                        svgElement.style.maxWidth = '100%';
                        svgElement.style.maxHeight = '100%';
                        svgElement.setAttribute('preserveAspectRatio', 'xMidYMid meet');

                        svgContainer.innerHTML = '';
                        svgContainer.appendChild(svgElement);
                    } catch (error) {
                        console.error('SVG 解析错误:', error);
                        svgContainer.innerHTML = `<p style="color: red; text-align: center; font-family: Arial, sans-serif;">SVG 解析错误: ${error.message}</p>`;
                    }
                } else if (renderMode === 'html') {
                    const previewDocument = previewFrame.contentDocument || previewFrame.contentWindow.document;
                    previewDocument.open();
                    previewDocument.write(code);
                    previewDocument.close();

                        // 根据开关渲染内嵌 Mermaid
                        try {
                            const shouldRender = (renderEmbeddedMermaid === 'on') || (renderEmbeddedMermaid === 'auto' && detectEmbeddedMermaidIn(code, 'html'));
                            if (shouldRender) {
                                renderEmbeddedMermaidInDoc(previewDocument);
                            }
                        } catch (_) {}

                } else if (renderMode === 'markdown') {
                    try {
                        // 按需检查依赖是否存在
                        if (!(window.marked && window.DOMPurify && window.hljs)) {
                            ensureMarkdownLibs().then(() => setTimeout(updatePreview, 0)).catch(() => {
                                const doc = previewFrame.contentDocument || previewFrame.contentWindow.document;
                                const safe = code.replace(/[&<>]/g, c => ({'&':'&amp;','<':'&lt;','>':'&gt;'}[c]));
                                doc.open();
                                doc.write(`<!DOCTYPE html><meta charset='utf-8'><pre>${safe}</pre>`);
                                doc.close();
                            });
                            return;
                        }
                        // 1) 解析 Markdown → HTML
                        let html = '';
                        try {
                            if (typeof marked !== 'undefined') {
                                // 高亮回调
                                marked.setOptions({
                                    gfm: true,
                                    breaks: true,
                                    mangle: false,
                                    headerIds: true,
                                    highlight: function(src, lang) {
                                        try {
                                            if (typeof hljs !== 'undefined') {
                                                if (lang && hljs.getLanguage(lang)) {
                                                    return hljs.highlight(src, { language: lang }).value;
                                                } else {
                                                    return hljs.highlightAuto(src).value;
                                                }
                                            }
                                        } catch (_) {}
                                        return src;
                                    }
                                });
                                html = marked.parse(code);
                            } else {
                                html = code; // 兜底：直接显示为文本
                            }
                        } catch (e) {
                            html = `<pre>${code.replace(/[&<>]/g, c => ({'&':'&amp;','<':'&lt;','>':'&gt;'}[c]))}</pre>`;
                        }

                        // 2) 安全净化
                        try {
                            if (typeof DOMPurify !== 'undefined') {
                                html = DOMPurify.sanitize(html, { ADD_ATTR: ['class'] });
                            }
                        } catch (_) {}

                        // 3) 写入 iframe 文档，注入样式与 highlight 主题
                        const doc = previewFrame.contentDocument || previewFrame.contentWindow.document;
                        const isDark = currentTheme === 'dark';
                        const style = `
                            :root { --accent: #ff7a18; --accent-600: #e66b00; --muted: ${isDark ? '#9aa0a6' : '#6a737d'}; --text: ${isDark ? '#e6e6e6' : '#222'}; --bg: ${isDark ? '#121212' : '#ffffff'}; --bg-code: ${isDark ? '#1e1e1e' : '#f6f8fa'}; }
                            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; padding: 18px; line-height: 1.8; color: var(--text); background: var(--bg); }
                            h1, h2, h3, h4, h5, h6 { margin: 1.2em 0 0.5em; line-height: 1.25; font-weight: 700; }
                            h1 { font-size: 2rem; color: var(--accent); border-bottom: 2px solid rgba(255,122,24,0.2); padding-bottom: 6px; }
                            h2 { font-size: 1.6rem; color: var(--accent-600); border-bottom: 1.5px solid rgba(255,122,24,0.18); padding-bottom: 4px; counter-reset: h3; }
                            h3 { font-size: 1.3rem; color: #333; }
                            h4 { font-size: 1.1rem; }
                            h5 { font-size: 1rem; }
                            h6 { font-size: 0.95rem; color: var(--muted); }
                            /* 编号体系：为层级标题添加编号（从 H2 起） */
                            body { counter-reset: h2; }
                            h2::before { counter-increment: h2; content: counter(h2) '. '; color: var(--accent-600); font-weight: 700; }
                            h3 { counter-reset: h4; }
                            h3::before { counter-increment: h3; content: counter(h2) '.' counter(h3) ' '; color: var(--accent-600); font-weight: 700; }
                            p { margin: 0 0 0.9em 0; }
                            ul, ol { margin: 0.4em 0 0.9em 1.2em; }
                            li { margin: 0.25em 0; }
                            hr { border: 0; height: 2px; background: linear-gradient(to right, var(--accent), rgba(255,122,24,0)); margin: 24px 0; }
                            pre { background: var(--bg-code); padding: 12px 14px; overflow: auto; border-radius: 8px; border-left: 3px solid var(--accent); }
                            code { background: rgba(27,31,35,.05); padding: 0.2em 0.4em; border-radius: 4px; font-size: 0.95em; }
                            pre code { background: transparent; padding: 0; }
                            table { border-collapse: collapse; margin: 1em 0; width: 100%; }
                            th { background: ${isDark ? '#33261a' : '#fff3e6'}; color: ${isDark ? '#ddd' : '#333'}; }
                            th, td { border: 1px solid ${isDark ? '#4a3b2f' : '#f0c49a'}; padding: 8px 10px; }
                            blockquote { color: var(--muted); border-left: 4px solid var(--accent); margin: 0.9em 0; padding: 0.4em 1em; background: ${isDark ? '#1a140f' : '#fff7f0'}; }
                            img { max-width: 100%; display: block; margin: 0.6em 0; }
                            .mermaid, .mermaid svg { max-width: 100%; height: auto; }
                            a { color: var(--accent-600); text-decoration: none; }
                            a:hover { text-decoration: underline; }
                        `;
                        const highlightTheme = isDark
                            ? 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css'
                            : 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css';
                        const htmlDoc = `<!DOCTYPE html><html><head><meta charset="utf-8"><link rel="stylesheet" href="${highlightTheme}"><style>${style}</style></head><body>${html}</body></html>`;
                        doc.open();
                        doc.write(htmlDoc);
                        doc.close();

                        // 4) 在 iframe 内渲染 mermaid 围栏
                        try {
                            const codeBlocks = Array.from(doc.querySelectorAll('pre > code.language-mermaid, code.language-mermaid'));
                            if (codeBlocks.length) {
                                if (!window.__mermaidInited && typeof mermaid !== 'undefined' && mermaid.initialize) {
                                    try {
                                        mermaid.initialize({
                                            startOnLoad: false,
                                            securityLevel: 'loose',
                                            theme: 'default',
                                            themeVariables: {
                                                primaryColor: '#ff7a18',
                                                primaryTextColor: '#222222',
                                                lineColor: '#ff7a18',
                                                secondaryColor: '#ffe8d6',
                                                tertiaryColor: '#fff3e6',
                                                fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif"
                                            }
                                        });
                                        window.__mermaidInited = true;
                                    } catch(_) {}
                                }
                                // 根据开关决定是否渲染内嵌 Mermaid
                                const shouldRender = (renderEmbeddedMermaid === 'on') || (renderEmbeddedMermaid === 'auto' && detectEmbeddedMermaidIn(code, 'markdown'));
                                if (shouldRender) {
                                    renderEmbeddedMermaidInDoc(doc);
                                }
                            }
                        } catch (_) {}
                    } catch (error) {
                        console.error('Markdown 渲染错误:', error);
                        const doc = previewFrame.contentDocument || previewFrame.contentWindow.document;
                        doc.open();
                        doc.write(`<pre style="color:red;">Markdown 渲染错误: ${error.message}</pre>`);
                        doc.close();
                    }
                } else {
                    showDefaultMessage();
                }
            }

            // 输入防抖
            let __debounceTimer = null;
            codeTextarea.addEventListener('input', function() {
                clearTimeout(__debounceTimer);
                __debounceTimer = setTimeout(() => {
                    try { localStorage.setItem('domedit:content', codeTextarea.value); } catch(_) {}
                    updatePreview();
                }, 300);
            });
            // 粘贴后尽快进行一次快速检测与渲染（不等待防抖）
            codeTextarea.addEventListener('paste', function() {
                setTimeout(() => {
                    try { localStorage.setItem('domedit:content', codeTextarea.value); } catch(_) {}
                    updatePreview();
                }, 0);
            });
            // 内嵌 Mermaid 开关变化
            mermaidSwitch.addEventListener('change', function() {
                renderEmbeddedMermaid = mermaidSwitch.value || 'auto';
                try { localStorage.setItem('domedit:renderMermaid', renderEmbeddedMermaid); } catch (_) {}
                updatePreview();
            });


            // 修改模式切换事件监听器
            modeSwitch.addEventListener('change', function() {
                // 如果是程序自动更新显示值，不执行switchMode
                if (window._isUpdatingModeDisplay) {
                    return;
                }
                switchMode(this.value);
                try { localStorage.setItem('domedit:mode', this.value); } catch(_) {}
            });
            // 主题切换按钮
            document.getElementById('themeToggle')?.addEventListener('click', function() {
                setTheme(currentTheme === 'dark' ? 'light' : 'dark');
            });

            // 文件导入功能
            function handleFileImport(file) {
                if (!file) return;

                // 检查文件类型
                const validExtensions = ['.md', '.svg', '.html', '.htm', '.mmd', '.txt'];
                const fileName = file.name.toLowerCase();
                const isValidFile = validExtensions.some(ext => fileName.endsWith(ext));

                if (!isValidFile) {
                    notify('不支持的文件类型，请选择 .md/.svg/.html/.mmd/.txt 文件', 'error');
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const content = e.target.result;
                    codeTextarea.value = content;

                    // 保存内容到本地存储
                    try { localStorage.setItem('domedit:content', content); } catch(_) {}

                    // 自动检测模式并切换
                    const detectedMode = detectCodeType(content);
                    if (detectedMode !== 'unknown') {
                        switchMode(detectedMode);
                    } else {
                        // 根据文件扩展名推断模式
                        if (fileName.endsWith('.md')) {
                            switchMode('markdown');
                        } else if (fileName.endsWith('.svg')) {
                            switchMode('svg');
                        } else if (fileName.endsWith('.mmd')) {
                            switchMode('mermaid');
                        } else if (fileName.endsWith('.html') || fileName.endsWith('.htm')) {
                            switchMode('html');
                        } else {
                            switchMode('auto');
                        }
                    }

                    notify(`已导入文件: ${file.name}`, 'success');
                };

                reader.onerror = function() {
                    notify('文件读取失败', 'error');
                };

                reader.readAsText(file, 'UTF-8');
            }

            // 打开文件按钮
            openFileBtn?.addEventListener('click', function() {
                fileInput.click();
            });

            // 示例模板按钮
            exampleBtn?.addEventListener('click', function() {
                showExampleMenu();
            });

            // 帮助按钮
            helpBtn?.addEventListener('click', function() {
                showHelpDialog();
            });

            // 显示帮助对话框
            function showHelpDialog() {
                const helpDialog = document.getElementById('helpDialog');
                helpDialog.style.display = 'flex';

                // 阻止页面滚动
                document.body.style.overflow = 'hidden';
            }

            // 关闭帮助对话框
            window.closeHelpDialog = function() {
                const helpDialog = document.getElementById('helpDialog');
                helpDialog.style.display = 'none';

                // 恢复页面滚动
                document.body.style.overflow = 'auto';
            };

            // 点击对话框外部关闭
            document.getElementById('helpDialog')?.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeHelpDialog();
                }
            });

            // 显示示例选择菜单
            function showExampleMenu() {
                const currentDetectedMode = currentMode === 'auto' ? detectCodeType(codeTextarea.value) : currentMode;
                const availableModes = ['html', 'markdown', 'svg', 'mermaid'];
                const isDark = currentTheme === 'dark';

                const menuBg = isDark ? '#2a2a2a' : 'white';
                const menuBorder = isDark ? '#444' : '#ccc';
                const titleColor = isDark ? '#e6e6e6' : '#333';
                const itemColor = isDark ? '#ccc' : '#666';
                const hoverBg = isDark ? '#3a3a3a' : '#f5f5f5';
                const activeBg = isDark ? '#33261a' : '#fff3e6';

                let menuHtml = `<div style="background: ${menuBg}; border: 1px solid ${menuBorder}; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); padding: 12px; min-width: 200px;">`;
                menuHtml += `<div style="font-weight: bold; margin-bottom: 8px; color: ${titleColor}; font-size: 14px;">选择示例模板</div>`;

                availableModes.forEach(mode => {
                    const template = exampleTemplates[mode];
                    if (template) {
                        const isCurrentMode = mode === currentDetectedMode || (currentDetectedMode === 'unknown' && mode === 'html');
                        menuHtml += `<div onclick="insertExample('${mode}')" style="padding: 8px 12px; cursor: pointer; border-radius: 4px; margin: 2px 0; font-size: 13px; ${isCurrentMode ? `background: ${activeBg}; color: #ff7a18; font-weight: 500;` : `color: ${itemColor};`}" onmouseover="this.style.background='${hoverBg}'" onmouseout="this.style.background='${isCurrentMode ? activeBg : 'transparent'}'">${template.title}</div>`;
                    }
                });

                menuHtml += '</div>';

                // 创建临时菜单
                const menu = document.createElement('div');
                menu.innerHTML = menuHtml;
                menu.style.position = 'fixed';
                menu.style.top = '60px';
                menu.style.right = '20px';
                menu.style.zIndex = '10000';
                menu.id = 'exampleMenu';

                // 移除已存在的菜单
                const existingMenu = document.getElementById('exampleMenu');
                if (existingMenu) {
                    existingMenu.remove();
                }

                document.body.appendChild(menu);

                // 点击外部关闭菜单
                setTimeout(() => {
                    document.addEventListener('click', function closeMenu(e) {
                        if (!menu.contains(e.target) && e.target !== exampleBtn) {
                            menu.remove();
                            document.removeEventListener('click', closeMenu);
                        }
                    });
                }, 100);
            }

            // 插入示例模板
            window.insertExample = function(mode) {
                const template = exampleTemplates[mode];
                if (!template) return;

                // 如果编辑器有内容，询问是否替换
                if (codeTextarea.value.trim() && !confirm(`确定要替换当前内容为 ${template.title} 吗？`)) {
                    return;
                }

                codeTextarea.value = template.content;

                // 保存到本地存储
                try { localStorage.setItem('domedit:content', template.content); } catch(_) {}

                // 切换到对应模式并更新预览
                switchMode(mode);

                // 关闭菜单
                const menu = document.getElementById('exampleMenu');
                if (menu) menu.remove();

                notify(`已插入 ${template.title}`, 'success');
            };

            // 导出主题选择功能
            document.getElementById('exportThemeToggle')?.addEventListener('click', function(e) {
                e.stopPropagation();
                showExportThemeMenu();
            });

            function showExportThemeMenu() {
                // 移除已存在的菜单
                const existingMenu = document.getElementById('exportThemeMenu');
                if (existingMenu) {
                    existingMenu.remove();
                    return;
                }

                const isDark = currentTheme === 'dark';
                const menuBg = isDark ? '#2a2a2a' : 'white';
                const menuBorder = isDark ? '#444' : '#ccc';
                const titleColor = isDark ? '#e6e6e6' : '#333';

                const menuHtml = `
                    <div style="font-weight: bold; margin-bottom: 8px; color: ${titleColor}; font-size: 14px;">导出主题</div>
                    <div class="export-theme-option ${exportTheme === 'auto' ? 'active' : ''}" onclick="setExportTheme('auto')">
                        <span>🔄</span> 跟随当前主题
                    </div>
                    <div class="export-theme-option ${exportTheme === 'light' ? 'active' : ''}" onclick="setExportTheme('light')">
                        <span>☀️</span> 浅色主题
                    </div>
                    <div class="export-theme-option ${exportTheme === 'dark' ? 'active' : ''}" onclick="setExportTheme('dark')">
                        <span>🌙</span> 深色主题
                    </div>
                `;

                const menu = document.createElement('div');
                menu.id = 'exportThemeMenu';
                menu.innerHTML = menuHtml;
                menu.style.position = 'fixed';
                menu.style.bottom = '80px';
                menu.style.right = '20px';
                menu.style.background = menuBg;
                menu.style.border = `1px solid ${menuBorder}`;
                menu.style.borderRadius = '8px';
                menu.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                menu.style.padding = '12px';
                menu.style.minWidth = '180px';
                menu.style.zIndex = '10001';
                menu.style.display = 'block';

                document.body.appendChild(menu);

                // 点击外部关闭菜单
                setTimeout(() => {
                    document.addEventListener('click', function closeMenu(e) {
                        if (!menu.contains(e.target) && e.target.id !== 'exportThemeToggle') {
                            menu.remove();
                            document.removeEventListener('click', closeMenu);
                        }
                    });
                }, 100);
            }

            // 设置导出主题
            window.setExportTheme = function(theme) {
                exportTheme = theme;
                try { localStorage.setItem('domedit:exportTheme', theme); } catch(_) {}

                const menu = document.getElementById('exportThemeMenu');
                if (menu) menu.remove();

                const themeNames = {
                    'auto': '跟随当前主题',
                    'light': '浅色主题',
                    'dark': '深色主题'
                };

                notify(`导出主题已设置为: ${themeNames[theme]}`, 'success');
            };

            // 获取实际导出主题
            function getActualExportTheme() {
                if (exportTheme === 'auto') {
                    return currentTheme;
                }
                return exportTheme;
            }

            // 文件选择处理
            fileInput?.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    handleFileImport(file);
                }
                // 清空input，允许重复选择同一文件
                e.target.value = '';
            });

            // 拖拽文件功能
            let dragCounter = 0;

            // 防止默认拖拽行为
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                editor.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            // 拖拽进入
            editor.addEventListener('dragenter', function(e) {
                dragCounter++;
                editor.classList.add('drag-over');
            });

            // 拖拽离开
            editor.addEventListener('dragleave', function(e) {
                dragCounter--;
                if (dragCounter === 0) {
                    editor.classList.remove('drag-over');
                }
            });

            // 拖拽悬停
            editor.addEventListener('dragover', function(e) {
                e.dataTransfer.dropEffect = 'copy';
            });

            // 文件放置
            editor.addEventListener('drop', function(e) {
                dragCounter = 0;
                editor.classList.remove('drag-over');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileImport(files[0]);
                }
            });

            // 初始化预览（恢复上次状态）
            window.addEventListener('load', function() {
                try {
                    const saved = localStorage.getItem('domedit:content');
                    if (saved) codeTextarea.value = saved;
                } catch(_) {}
                try {
                    const savedMode = localStorage.getItem('domedit:mode');
                    if (savedMode) currentMode = savedMode;
                } catch(_) {}
                try {
                    const savedWidth = parseInt(localStorage.getItem('domedit:editorWidth') || '', 10);
                    if (!isNaN(savedWidth)) {
                        const containerRect = container.getBoundingClientRect();
                        const min = 100;
                        const max = containerRect.width - 100 - divider.offsetWidth;
                        const clamped = Math.min(Math.max(savedWidth, min), max);
                        editor.style.width = `${clamped}px`;
                        preview.style.width = `${containerRect.width - clamped - divider.offsetWidth}px`;
                    }
                } catch(_) {}
                try { currentTheme = localStorage.getItem('domedit:theme') || 'light'; } catch(_) {}
                try { exportTheme = localStorage.getItem('domedit:exportTheme') || 'auto'; } catch(_) {}
                // 应用主题并渲染
                try {
                    setTheme(currentTheme);
                    // 确保按钮显示正确的图标
                    if (currentTheme === 'dark') {
                        themeToggle.textContent = '☀️';
                        themeToggle.title = '切换到浅色主题';
                    } else {
                        themeToggle.textContent = '🌙';
                        themeToggle.title = '切换到暗色主题';
                    }
                } catch(_) {}
                // 若没有保存的模式，保持 auto；并根据现有内容立即渲染
                if (!currentMode) currentMode = 'auto';
                modeSwitch.value = currentMode;
                if (codeTextarea.value.trim()) {
                    updatePreview();
                } else {
                    switchMode(currentMode);
                    showDefaultMessage();
                }
            });

            // 分条拖拽功能
            let isResizing = false;

            divider.addEventListener('mousedown', function(e) {
                isResizing = true;
                document.body.style.cursor = 'col-resize';
            });

            // 双击分隔条复位到50/50
            divider.addEventListener('dblclick', function(e) {
                e.preventDefault();
                const containerRect = container.getBoundingClientRect();
                const newEditorWidth = (containerRect.width - divider.offsetWidth) / 2;

                editor.style.width = `${newEditorWidth}px`;
                preview.style.width = `${newEditorWidth}px`;
                divider.setAttribute('aria-valuenow', String(Math.round(newEditorWidth)));

                // 保存到本地存储
                try {
                    localStorage.setItem('domedit:editorWidth', String(Math.round(newEditorWidth)));
                } catch(_) {}

                notify('布局已复位到 50/50', 'success');
            });

            document.addEventListener('mousemove', function(e) {
                if (!isResizing) return;
                const containerRect = container.getBoundingClientRect();
                let newEditorWidth = e.clientX - containerRect.left;

                // 设置最小宽度
                const editorMinWidth = 100;
                const previewMinWidth = 100;
                const maxEditorWidth = containerRect.width - previewMinWidth - divider.offsetWidth;

                if (newEditorWidth < editorMinWidth) {
                    newEditorWidth = editorMinWidth;
                } else if (newEditorWidth > maxEditorWidth) {
                    newEditorWidth = maxEditorWidth;
                }

                editor.style.width = `${newEditorWidth}px`;
                preview.style.width = `${containerRect.width - newEditorWidth - divider.offsetWidth}px`;
                divider.setAttribute('aria-valuenow', String(Math.round(newEditorWidth)));
            });

            document.addEventListener('mouseup', function() {
                isResizing = false;
                document.body.style.cursor = 'default';
                try {
                    const width = editor.getBoundingClientRect().width;
                    localStorage.setItem('domedit:editorWidth', String(Math.round(width)));
                } catch(_) {}
            });

            // 分隔条键盘可访问性
            divider.addEventListener('keydown', function(e) {
                const step = (e.shiftKey ? 50 : 10);
                const containerRect = container.getBoundingClientRect();
                const currentWidth = editor.getBoundingClientRect().width;
                let newEditorWidth = currentWidth;
                if (e.key === 'ArrowLeft') {
                    newEditorWidth = currentWidth - step;
                } else if (e.key === 'ArrowRight') {
                    newEditorWidth = currentWidth + step;
                } else if (e.key === 'Home') {
                    newEditorWidth = 100; // 最小
                } else if (e.key === 'End') {
                    newEditorWidth = containerRect.width - 100 - divider.offsetWidth; // 最大
                } else {
                    return; // 其它键不处理
                }

                const editorMinWidth = 100;
                const previewMinWidth = 100;
                const maxEditorWidth = containerRect.width - previewMinWidth - divider.offsetWidth;
                if (newEditorWidth < editorMinWidth) newEditorWidth = editorMinWidth;
                if (newEditorWidth > maxEditorWidth) newEditorWidth = maxEditorWidth;

                editor.style.width = `${newEditorWidth}px`;
                preview.style.width = `${containerRect.width - newEditorWidth - divider.offsetWidth}px`;
                divider.setAttribute('aria-valuenow', String(Math.round(newEditorWidth)));
                e.preventDefault();
                try { localStorage.setItem('domedit:editorWidth', String(Math.round(newEditorWidth))); } catch(_) {}
            });
            // 双击分隔条：重置为 50/50 并保存
            divider.addEventListener('dblclick', function() {
                const rect = container.getBoundingClientRect();
                const half = Math.round((rect.width - divider.offsetWidth) / 2);
                editor.style.width = `${half}px`;
                preview.style.width = `${rect.width - half - divider.offsetWidth}px`;
                try { localStorage.setItem('domedit:editorWidth', String(half)); } catch(_) {}
            });

            // 修改下载代码功能
            document.getElementById('downloadCode').addEventListener('click', function() {
                const code = codeTextarea.value;
                const blob = new Blob([code], {type: 'text/plain;charset=utf-8'});
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                const timestamp = getShortTimestamp();

                let modeForName = currentMode;
                if (modeForName === 'auto') {
                    const detected = detectCodeType(code);
                    modeForName = detected === 'unknown' ? 'txt' : detected;
                }
                let fileName = generateFileName(code, modeForName, timestamp);
                if (modeForName === 'html' || modeForName === 'markdown') {
                    const doc = previewFrame.contentDocument;
                    const first = findFirstElementWithContent(doc && doc.body ? doc.body : document.body);
                    if (first) fileName = `${slugify(first)}_${timestamp}.${modeForName === 'markdown' ? 'md' : 'html'}`;
                } else if (modeForName === 'svg') {
                    const svgElement = svgContainer.querySelector('svg');
                    const first = svgElement ? findFirstElementWithContent(svgElement) : null;
                    if (first) fileName = `${slugify(first)}_${timestamp}.svg`;
                }

                link.download = fileName;
                link.href = url;
                link.click();
                URL.revokeObjectURL(url);
            });

            // 修改 handleHtmlToPng 函数
            function handleHtmlToPng(callback) {
                if (!previewFrame.contentDocument || !previewFrame.contentDocument.body) {
                    console.error('预览框架未准备好');
                    notify('预览内容未加载完成，请稍后再试', 'error');
                    return;
                }

                const doc = previewFrame.contentDocument;
                const actualExportTheme = getActualExportTheme();
                const exportDark = actualExportTheme === 'dark';

                setTimeout(() => {
                    const container = doc.body;

                    // 记录原始样式
                    const originalPadding = container.style.padding;
                    const originalBackground = container.style.backgroundColor;
                    const originalColor = container.style.color;

                    // 根据导出主题设置样式
                    const bgColor = exportDark ? '#1a1a1a' : '#f5f5f5';
                    const textColor = exportDark ? '#e6e6e6' : '#333';

                    container.style.padding = '5px';
                    container.style.backgroundColor = bgColor;
                    container.style.color = textColor;

                    const options = {
                        scale: window.devicePixelRatio * 2,
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: bgColor,
                        logging: true,
                        onclone: function(clonedDoc) {
                            const clonedBody = clonedDoc.body;
                            clonedBody.style.padding = '5px';
                            clonedBody.style.backgroundColor = bgColor;
                            clonedBody.style.color = textColor;
                        }
                    };

                    html2canvas(container, options)
                        .then(canvas => {
                            // 恢复原始样式
                            container.style.padding = originalPadding;
                            container.style.backgroundColor = originalBackground;
                            container.style.color = originalColor;

                            callback(canvas);
                        })
                        .catch(err => {
                            console.error('html2canvas 失败:', err);
                            // 恢复原始样式
                            container.style.padding = originalPadding;
                            container.style.backgroundColor = originalBackground;
                            container.style.color = originalColor;

                            notify('生成PNG图片失败：' + err.message, 'error');
                        });
                }, 500);
            }

            // 修改 handleSvgToPng 函数
            function handleSvgToPng(callback) {
                // 给一个小延迟确保 SVG 已经完全渲染
                setTimeout(() => {
                    let svgElement = svgContainer.querySelector('svg');
                    if (!svgElement) {
                        alert('没有找到有效的SVG元素，请检查您的代码。');
                        return;
                    }

                    // 创建一个新的 canvas，设置合适的尺寸
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    // 获取 SVG 的原始尺寸
                    let svgWidthAttr = svgElement.getAttribute('width');
                    let svgHeightAttr = svgElement.getAttribute('height');
                    let vb = svgElement.viewBox && svgElement.viewBox.baseVal ? svgElement.viewBox.baseVal : null;
                    let svgWidth = (svgWidthAttr && !/%/.test(svgWidthAttr)) ? parseFloat(svgWidthAttr) : (vb ? vb.width : svgElement.getBoundingClientRect().width || 800);
                    let svgHeight = (svgHeightAttr && !/%/.test(svgHeightAttr)) ? parseFloat(svgHeightAttr) : (vb ? vb.height : svgElement.getBoundingClientRect().height || 600);

                    // 设置 canvas 尺寸为 SVG 的原始尺寸
                    const scale = 2; // 提高清晰度的缩放因子
                    canvas.width = svgWidth * scale;
                    canvas.height = svgHeight * scale;

                    // 填充白色背景
                    ctx.fillStyle = 'white';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);

                    // 缩放以提高清晰度
                    ctx.scale(scale, scale);

                    // 将 SVG 转换为图片
                        const cloned = svgElement.cloneNode(true);
                        sanitizeSvgElement(cloned);
                        const svgData = new XMLSerializer().serializeToString(cloned);
                    const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                    const url = URL.createObjectURL(svgBlob);

                    const img = new Image();
                    img.onload = function() {
                        ctx.drawImage(img, 0, 0);
                        URL.revokeObjectURL(url);
                        callback(canvas);
                    };

                    img.onerror = function(error) {
                        console.error('图片加载失败:', error);
                        URL.revokeObjectURL(url);
                        alert('图片加载失败，请检查SVG代码是否正确。');
                    };

                    img.src = url;
                }, 100);
            }

            // 修改 findFirstElementWithContent 数
            function findFirstElementWithContent(element) {
                // 如果元素是文本节点，直接返回其内容
                if (element.nodeType === Node.TEXT_NODE && element.textContent.trim()) {
                    return element.textContent.trim();
                }

                // 检查当前元素的直接文本内容（不包括子元素的文本）
                for (let node of element.childNodes) {
                    if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
                        return node.textContent.trim();
                    }
                }

                // 如果当前元素没有直接文本内容，递归检查子元素
                for (let child of element.children) {

                    const result = findFirstElementWithContent(child);
                    if (result) {
                        return result;
                    }
                }

                return null;
            }

            // 安全处理：清理 SVG 中的潜在危险内容
            function sanitizeSvgElement(root) {
                try {
                    // 移除危险标签
                    ['script', 'foreignObject'].forEach(tag => {
                        root.querySelectorAll(tag).forEach(n => n.remove());
                    });
                    // 递归清除事件处理与可疑链接
                    (function walk(el){
                        if (!el || !el.attributes) return;
                        for (let i = el.attributes.length - 1; i >= 0; i--) {
                            const name = el.attributes[i].name;
                            const value = el.attributes[i].value || '';
                            if (/^on/i.test(name)) {
                                el.removeAttribute(name);
                            } else if ((name === 'href' || name === 'xlink:href') && /^\s*javascript:/i.test(value)) {
                                el.removeAttribute(name);
                            }
                        }
                        for (const child of el.children || []) walk(child);
                    })(root);
                } catch (_) { /* 忽略清理错误，尽量不中断流程 */ }
            }

            // 简易 slug 化，适用于文件名
            function slugify(text) {
                try {
                    return String(text)
                        .trim()
                        .toLowerCase()
                        .replace(/[\s\t\n]+/g, '_')
                        .replace(/[^a-z0-9_\-.\u4e00-\u9fa5]/gi, '')
                        .slice(0, 64) || 'file';
                } catch (_) { return 'file'; }
            }

            // 添加新函数专门处理 Mermaid 图表的截图
            function handleMermaidToPng(callback) {
                // 直接使用 SVG 流程，保持清晰度
                if (!window.mermaidRendered) {
                    setTimeout(() => handleMermaidToPng(callback), 100);
                    return;
                }
                return handleSvgToPng(callback);
            }

            // 修改复制 PNG 功能的事件监听器
            document.getElementById('copyPng').addEventListener('click', function() {
                const proceed = (canvas) => handleCanvasToClipboard(canvas);
                const code = codeTextarea.value.trim();
                const effectiveMode = currentMode === 'auto' ? detectCodeType(code) : currentMode;

                console.log('复制PNG - 当前模式:', currentMode, '有效模式:', effectiveMode, '代码长度:', code.length);

                if (!code) {
                    notify('请先输入内容再复制PNG', 'error');
                    return;
                }

                if (effectiveMode === 'html' || effectiveMode === 'markdown') {
                    console.log('处理HTML/Markdown模式的PNG复制');
                    setTimeout(() => handleHtmlToPng(proceed), 100);
                } else if (effectiveMode === 'svg') {
                    console.log('处理SVG模式的PNG复制');
                    handleSvgToPng(proceed);
            // 检测 HTML/Markdown 中是否含有 Mermaid 片段
            function detectEmbeddedMermaidIn(code, mode) {
                if (!code) return false;
                try {
                    if (mode === 'markdown') {
                        return /```\s*mermaid\b[\s\S]*?```/i.test(code);
                    }
                    if (mode === 'html') {
                        const head = code.slice(0, 8000);
                        return /<pre[^>]*>\s*<code[^>]*class\s*=\s*"[^"]*\b(language-)?mermaid\b[^"]*"[\s\S]*?<\/code>\s*<\/pre>/i.test(head)
                            || /<code[^>]*class\s*=\s*"[^"]*\bmermaid\b[^"]*"[\s\S]*?<\/code>/i.test(head)
                            || /<div[^>]*class\s*=\s*"[^"]*\bmermaid\b[^"]*"[\s\S]*?<\/div>/i.test(head);
                    }
                } catch (_) {}
                return false;
            }

            // 在 iframe(HTML/Markdown) 中渲染内嵌 Mermaid 片段
            async function renderEmbeddedMermaidInDoc(doc) {
                if (typeof mermaid === 'undefined' || !mermaid.render) return;
                try {
                    if (!window.__mermaidInited && mermaid.initialize) {
                        mermaid.initialize({ startOnLoad: false, securityLevel: 'loose', theme: 'default' });
                        window.__mermaidInited = true;
                    }
                } catch (_) {}
                const candidates = Array.from(doc.querySelectorAll('pre > code.language-mermaid, code.language-mermaid, code.mermaid, div.mermaid'));
                for (let i = 0; i < candidates.length; i++) {
                    const node = candidates[i];
                    const raw = node.textContent || '';
                    const id = `embed-mermaid-${Date.now()}-${i}`;
                    try {
                        const { svg } = await mermaid.render(id, raw);
                        const wrapper = doc.createElement('div');
                        wrapper.className = 'mermaid';
                        wrapper.innerHTML = svg;
                        const pre = node.closest && node.closest('pre');
                        if (pre && pre.parentNode) pre.parentNode.replaceChild(wrapper, pre);
                        else if (node.parentNode) node.parentNode.replaceChild(wrapper, node);
                    } catch (error) {
                        const err = doc.createElement('p');
                        err.style.color = 'red';
                        err.textContent = 'Mermaid 渲染错误: ' + (error && error.message ? error.message : String(error));
                        const pre = node.closest && node.closest('pre');
                        if (pre && pre.parentNode) pre.parentNode.replaceChild(err, pre);
                    }
                }
            }

                } else if (effectiveMode === 'mermaid') {
                    console.log('处理Mermaid模式的PNG复制');
                    handleMermaidToPng(proceed);
                } else {
                    console.log('不支持的模式:', effectiveMode);
                    notify('当前内容类型不支持PNG复制', 'error');
                }
            });

            // 添加一个新的辅助函数来处理画布到剪贴板的转换
            function handleCanvasToClipboard(canvas) {
                // 9. 添加更详细的调试信息
                console.log('Handling canvas to clipboard');
                console.log('Canvas dimensions:', canvas.width, canvas.height);

                // 10. 验证画布内容
                const ctx = canvas.getContext('2d');
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const hasContent = imageData.data.some(x => x !== 0);
                console.log('Canvas has content:', hasContent);

                if (!hasContent) {
                    console.error('Canvas is empty');
                    alert('生成的图片为空，请检查预览内容是否正确显示。');
                    return;
                }

                // 11. 确保画布尺寸合理
                if (canvas.width < 50 || canvas.height < 50) {
                    console.error('Canvas dimensions too small');
                    alert('生成的图片尺寸异常，请检查预览内容。');
                    return;
                }

                canvas.toBlob(function(blob) {
                    if (!blob || blob.size === 0) {
                        console.error('Generated blob is empty');
                        notify('生成图片失败：图片数据为空', 'error');
                        return;
                    }

                    console.log('Blob size:', blob.size);

                    if (navigator.clipboard && window.ClipboardItem) {
                        const data = new ClipboardItem({ 'image/png': blob });
                        navigator.clipboard.write([data])
                            .then(() => {
                                console.log('Successfully copied to clipboard');
                                showNotification();
                            })
                            .catch(err => {
                                console.error('复制到剪贴板失败:', err);
                                notify('复制失败，已为您下载 PNG 文件。', 'error');
                                downloadBlob(blob, `screenshot_${getShortTimestamp()}.png`);
                            });
                    } else {
                        notify('浏览器不支持剪贴板复制，已为您下载 PNG 文件。', 'error');
                        downloadBlob(blob, `screenshot_${getShortTimestamp()}.png`);
                    }
                }, 'image/png', 1.0);
            }

            function generateImageFileName(code, mode, timestamp) {
                let lower = code.trim().toLowerCase();
                if (mode === 'mermaid') {
                    if (lower.includes('graph') || lower.includes('flowchart')) return `flowchart_${timestamp}.png`;
                    if (lower.includes('sequencediagram')) return `sequence_${timestamp}.png`;
                    if (lower.includes('gantt')) return `gantt_${timestamp}.png`;
                    if (lower.includes('classdiagram')) return `class_${timestamp}.png`;
                    if (lower.startsWith('pie') || lower.includes('pie title')) return `pie_${timestamp}.png`;
                    if (lower.includes('statediagram')) return `state_${timestamp}.png`;
                    if (lower.includes('journey')) return `journey_${timestamp}.png`;
                    if (lower.includes('erdiagram')) return `er_${timestamp}.png`;
                    if (lower.includes('gitgraph')) return `git_${timestamp}.png`;
                    if (lower.includes('mindmap')) return `mindmap_${timestamp}.png`;
                    return `diagram_${timestamp}.png`;
                }
                return `screenshot_${timestamp}.png`;
            }

            // 修改下载 PNG 的事件监听器（统一命名与流程）
            document.getElementById('downloadPng').addEventListener('click', function() {
                const timestamp = getShortTimestamp();
                const code = codeTextarea.value || '';
                let modeForName = currentMode === 'auto' ? detectCodeType(code) : currentMode;

                const handleBlobDownload = (blob) => {
                    let fileName = generateImageFileName(code, modeForName, timestamp);
                    if (modeForName === 'html' || modeForName === 'markdown') {
                        const doc = previewFrame.contentDocument;
                        let base = findFirstElementWithContent(doc && doc.body ? doc.body : document.body) || 'screenshot';
                        fileName = slugify(base) + '.png';
                    } else if (modeForName === 'svg') {
                        const svgElement = svgContainer.querySelector('svg');
                        let base = svgElement ? findFirstElementWithContent(svgElement) : 'diagram';
                        fileName = slugify(base) + '.png';
                    }
                    downloadBlob(blob, fileName);
                };

                if (modeForName === 'mermaid') {
                    handleMermaidToPng((canvas) => {
                        canvas.toBlob(handleBlobDownload, 'image/png', 1.0);
                    });
                } else if (modeForName === 'svg') {
                    handleSvgToPng((canvas) => {
                        canvas.toBlob(handleBlobDownload, 'image/png', 1.0);
                    });
                } else {
                    setTimeout(() => {
                        handleHtmlToPng((canvas) => {
                            canvas.toBlob(handleBlobDownload, 'image/png', 1.0);
                        });
                    }, 100);
                }
            });

            // 辅助函数：下载 Blob
            function downloadBlob(blob, fileName) {
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = fileName;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
            }

            // 通知：统一成功/错误样式
            function notify(message, type = 'success') {
                notification.textContent = message || '';
                notification.style.backgroundColor = (type === 'error') ? '#E53935' : '#4CAF50';
                notification.style.display = 'block';
                notification.classList.add('show');

                // 自动隐藏通知
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        notification.style.display = 'none';
                    }, 300);
                }, type === 'error' ? 4000 : 3000); // 错误消息显示更长时间
            }

            function showNotification() {
                notify('PNG 已复制到剪贴板', 'success');
            }

            // 添加下载GIF功能
            document.getElementById('downloadGif').addEventListener('click', function() {
                this.disabled = true; // 禁用按钮
                const code = codeTextarea.value.trim();
                const effectiveMode = currentMode === 'auto' ? detectCodeType(code) : currentMode;

                if (effectiveMode === 'html' || effectiveMode === 'markdown') {
                    captureAnimation(previewFrame.contentDocument.body);
                } else if (effectiveMode === 'svg' || effectiveMode === 'mermaid') {
                    const svgElement = svgContainer.querySelector('svg');
                    if (svgElement) {
                        captureAnimation(svgElement);
                    } else {
                        alert('没有找到有效的SVG元素，请检查您的SVG代码。');
                        this.disabled = false; // 如果出错,重新启用按钮
                    }
                } else {
                    alert('当前内容类型不支持GIF导出。');
                    this.disabled = false;
                }
            });

            // 复制文本（HTML/Markdown 原文）
            document.getElementById('copyText').addEventListener('click', async function() {
                const text = codeTextarea.value || '';
                try { await navigator.clipboard.writeText(text); notify('文本已复制'); } catch(e) { notify('复制失败：' + e.message, 'error'); }
            });

            // 复制渲染后的HTML（仅Markdown模式）
            document.getElementById('copyHtml').addEventListener('click', async function() {
                const code = codeTextarea.value.trim();
                const effectiveMode = currentMode === 'auto' ? detectCodeType(code) : currentMode;

                if (!code) {
                    notify('请先输入Markdown内容', 'error');
                    return;
                }

                if (effectiveMode !== 'markdown') {
                    notify('此功能仅在Markdown模式下可用', 'error');
                    return;
                }

                try {
                    // 检查预览框架是否已渲染
                    const doc = previewFrame.contentDocument || previewFrame.contentWindow.document;
                    if (!doc || !doc.body) {
                        notify('Markdown内容尚未渲染完成，请稍后再试', 'error');
                        return;
                    }

                    // 获取渲染后的HTML内容
                    let htmlContent = doc.body.innerHTML;

                    // 清理一些不必要的属性和内容
                    htmlContent = htmlContent
                        .replace(/\s+id="[^"]*"/g, '') // 移除id属性
                        .replace(/\s+class="mermaid[^"]*"/g, ' class="mermaid"') // 简化mermaid类名
                        .replace(/<!--[\s\S]*?-->/g, '') // 移除HTML注释
                        .trim();

                    // 如果内容为空，提示用户
                    if (!htmlContent) {
                        notify('没有找到可复制的HTML内容', 'error');
                        return;
                    }

                    // 复制到剪贴板
                    await navigator.clipboard.writeText(htmlContent);
                    notify('渲染后的HTML已复制到剪贴板', 'success');

                } catch(e) {
                    console.error('复制HTML失败:', e);
                    notify('复制失败：' + e.message, 'error');
                }
            });

            // 下载SVG功能
            document.getElementById('downloadSvg').addEventListener('click', function() {
                const code = codeTextarea.value.trim();
                const effectiveMode = currentMode === 'auto' ? detectCodeType(code) : currentMode;

                if (!code) {
                    notify('请先输入内容再下载SVG', 'error');
                    return;
                }

                if (effectiveMode !== 'svg' && effectiveMode !== 'mermaid') {
                    notify('当前内容类型不支持SVG导出', 'error');
                    return;
                }

                let svgContent = '';
                let fileName = '';
                const timestamp = getShortTimestamp();

                if (effectiveMode === 'svg') {
                    // 直接使用SVG源码
                    svgContent = code;
                    const svgElement = svgContainer.querySelector('svg');
                    const titleText = svgElement ? findFirstElementWithContent(svgElement) : null;
                    fileName = titleText ? `${slugify(titleText)}_${timestamp}.svg` : `diagram_${timestamp}.svg`;
                } else if (effectiveMode === 'mermaid') {
                    // 获取渲染后的SVG
                    const svgElement = svgContainer.querySelector('svg');
                    if (!svgElement) {
                        notify('Mermaid图表尚未渲染完成，请稍后再试', 'error');
                        return;
                    }

                    // 克隆并清理SVG元素
                    const clonedSvg = svgElement.cloneNode(true);
                    sanitizeSvgElement(clonedSvg);

                    // 确保SVG有正确的命名空间
                    clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                    if (clonedSvg.querySelector('foreignObject')) {
                        clonedSvg.setAttribute('xmlns:xhtml', 'http://www.w3.org/1999/xhtml');
                    }

                    svgContent = new XMLSerializer().serializeToString(clonedSvg);

                    // 生成文件名
                    fileName = generateImageFileName(code, 'mermaid', timestamp).replace('.png', '.svg');
                }

                // 创建并下载文件
                const blob = new Blob([svgContent], { type: 'image/svg+xml;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = fileName;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                notify(`SVG文件已下载: ${fileName}`, 'success');
            });

            // 下载PDF功能
            document.getElementById('downloadPdf').addEventListener('click', function() {
                const code = codeTextarea.value.trim();
                const effectiveMode = currentMode === 'auto' ? detectCodeType(code) : currentMode;

                if (!code) {
                    notify('请先输入内容再下载PDF', 'error');
                    return;
                }

                generatePdf(effectiveMode);
            });

            // 全屏预览功能
            document.getElementById('fullscreenPreview').addEventListener('click', function() {
                const code = codeTextarea.value.trim();
                const effectiveMode = currentMode === 'auto' ? detectCodeType(code) : currentMode;

                if (!code) {
                    notify('请先输入内容再全屏预览', 'error');
                    return;
                }

                enterFullscreen(effectiveMode);
            });

            // 退出全屏
            document.getElementById('exitFullscreen').addEventListener('click', exitFullscreen);

            // 全局快捷键处理
            document.addEventListener('keydown', function(e) {
                // ESC键处理：优先关闭帮助对话框，然后退出全屏
                if (e.key === 'Escape') {
                    const helpDialog = document.getElementById('helpDialog');
                    if (helpDialog && helpDialog.style.display !== 'none') {
                        closeHelpDialog();
                        return;
                    }
                    if (document.getElementById('fullscreenContainer').style.display !== 'none') {
                        exitFullscreen();
                        return;
                    }
                }

                // 检查是否按下了 Ctrl (Windows/Linux) 或 Cmd (Mac)
                const isCtrlOrCmd = e.ctrlKey || e.metaKey;

                // 避免在输入框中触发某些快捷键
                const isInTextarea = e.target === codeTextarea;

                // Ctrl/Cmd + S: 下载源码
                if (isCtrlOrCmd && e.key === 's' && !e.shiftKey) {
                    e.preventDefault();
                    document.getElementById('downloadCode').click();
                    return;
                }

                // Ctrl/Cmd + Shift + S: 下载PNG
                if (isCtrlOrCmd && e.key === 'S' && e.shiftKey) {
                    e.preventDefault();
                    document.getElementById('downloadPng').click();
                    return;
                }

                // Ctrl/Cmd + P: 下载PDF
                if (isCtrlOrCmd && e.key === 'p') {
                    e.preventDefault();
                    document.getElementById('downloadPdf').click();
                    return;
                }

                // F11: 全屏预览 (阻止浏览器默认全屏行为)
                if (e.key === 'F11') {
                    e.preventDefault();
                    e.stopPropagation();

                    // 确保有内容才触发全屏预览
                    const code = codeTextarea.value.trim();
                    if (code) {
                        const effectiveMode = currentMode === 'auto' ? detectCodeType(code) : currentMode;
                        enterFullscreen(effectiveMode);
                    } else {
                        notify('请先输入内容再全屏预览', 'error');
                    }
                    return;
                }

                // Ctrl/Cmd + L: 清空编辑器 (不在输入框中时)
                if (isCtrlOrCmd && e.key === 'l' && !isInTextarea) {
                    e.preventDefault();
                    if (codeTextarea.value.trim() && confirm('确定要清空编辑器内容吗？')) {
                        codeTextarea.value = '';
                        try { localStorage.setItem('domedit:content', ''); } catch(_) {}
                        showDefaultMessage();
                        notify('编辑器已清空', 'success');
                    }
                    return;
                }

                // Ctrl/Cmd + /: 切换主题
                if (isCtrlOrCmd && e.key === '/') {
                    e.preventDefault();
                    document.getElementById('themeToggle').click();
                    return;
                }

                // Ctrl/Cmd + O: 打开文件 (不在输入框中时)
                if (isCtrlOrCmd && e.key === 'o' && !isInTextarea) {
                    e.preventDefault();
                    document.getElementById('openFile').click();
                    return;
                }
            });

            // PDF生成功能
            function generatePdf(mode) {
                const code = codeTextarea.value.trim();
                const timestamp = getShortTimestamp();
                const actualExportTheme = getActualExportTheme();
                const exportDark = actualExportTheme === 'dark';

                // 根据导出主题设置样式变量
                const bgColor = exportDark ? '#1a1a1a' : 'white';
                const textColor = exportDark ? '#e6e6e6' : '#333';
                const codeBlockBg = exportDark ? '#2d2d2d' : '#f6f8fa';
                const inlineCodeBg = exportDark ? 'rgba(255,255,255,0.1)' : 'rgba(27,31,35,.05)';
                const tableBorder = exportDark ? '#4a3b2f' : '#f0c49a';
                const tableHeaderBg = exportDark ? '#33261a' : '#fff3e6';
                const quoteBg = exportDark ? '#1a140f' : '#fff7f0';
                const quoteColor = exportDark ? '#9aa0a6' : '#6a737d';

                // 创建一个隐藏的打印窗口
                const printWindow = window.open('', '_blank', 'width=800,height=600');
                const pdfHtml = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>导出PDF - ${timestamp}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/${exportDark ? 'github-dark' : 'github'}.min.css">
    <style>
        :root {
            --accent: #ff7a18;
            --accent-600: #e66b00;
            --muted: ${quoteColor};
            --text: ${textColor};
            --bg: ${bgColor};
            --bg-code: ${codeBlockBg};
        }
        @page { margin: 1cm; size: A4; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.8;
            color: var(--text);
            margin: 0;
            padding: 18px;
            background: var(--bg);
        }
        h1, h2, h3, h4, h5, h6 { margin: 1.2em 0 0.5em; line-height: 1.25; font-weight: 700; }
        h1 { font-size: 2rem; color: var(--accent); border-bottom: 2px solid rgba(255,122,24,0.2); padding-bottom: 6px; }
        h2 { font-size: 1.6rem; color: var(--accent-600); border-bottom: 1.5px solid rgba(255,122,24,0.18); padding-bottom: 4px; counter-reset: h3; }
        h3 { font-size: 1.3rem; color: #333; }
        h4 { font-size: 1.1rem; }
        h5 { font-size: 1rem; }
        h6 { font-size: 0.95rem; color: var(--muted); }
        /* 编号体系：为层级标题添加编号（从 H2 起） */
        body { counter-reset: h2; }
        h2::before { counter-increment: h2; content: counter(h2) '. '; color: var(--accent-600); font-weight: 700; }
        h3 { counter-reset: h4; }
        h3::before { counter-increment: h3; content: counter(h2) '.' counter(h3) ' '; color: var(--accent-600); font-weight: 700; }
        p { margin: 0 0 0.9em 0; }
        ul, ol { margin: 0.4em 0 0.9em 1.2em; }
        li { margin: 0.25em 0; }
        hr { border: 0; height: 2px; background: linear-gradient(to right, var(--accent), rgba(255,122,24,0)); margin: 24px 0; }
        pre { background: var(--bg-code); padding: 12px 14px; overflow: auto; border-radius: 8px; border-left: 3px solid var(--accent); }
        code { background: ${inlineCodeBg}; padding: 0.2em 0.4em; border-radius: 4px; font-size: 0.95em; }
        pre code { background: transparent; padding: 0; }
        table { border-collapse: collapse; margin: 1em 0; width: 100%; }
        th { background: ${tableHeaderBg}; color: ${exportDark ? '#ddd' : '#333'}; }
        th, td { border: 1px solid ${tableBorder}; padding: 8px 10px; }
        blockquote { color: var(--muted); border-left: 4px solid var(--accent); margin: 0.9em 0; padding: 0.4em 1em; background: ${quoteBg}; }
        img { max-width: 100%; display: block; margin: 0.6em 0; }
        .mermaid, .mermaid svg { max-width: 100%; height: auto; }
        svg { max-width: 100%; height: auto; display: block; margin: 0 auto; }
        a { color: var(--accent-600); text-decoration: none; }
        a:hover { text-decoration: underline; }
        @media print {
            body { margin: 0; padding: 10px; }
            h1, h2, h3, h4, h5, h6 { page-break-after: avoid; }
            img, svg { page-break-inside: avoid; }
            pre, blockquote { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div id="pdfContent"></div>
</body>
</html>`;
                printWindow.document.write(pdfHtml);

                if (mode === 'html' || mode === 'markdown') {
                    // 复制当前预览内容到打印窗口
                    const currentDoc = previewFrame.contentDocument;
                    if (currentDoc && currentDoc.body) {
                        printWindow.document.getElementById('pdfContent').innerHTML = currentDoc.body.innerHTML;
                    }
                } else if (mode === 'svg' || mode === 'mermaid') {
                    // 复制SVG内容到打印窗口
                    const svgElement = svgContainer.querySelector('svg');
                    if (svgElement) {
                        printWindow.document.getElementById('pdfContent').appendChild(svgElement.cloneNode(true));
                    }
                }

                // 等待内容加载完成后打印
                setTimeout(() => {
                    printWindow.focus();
                    printWindow.print();
                    // 打印对话框关闭后关闭窗口
                    setTimeout(() => {
                        printWindow.close();
                    }, 100);
                }, 500);

                notify('PDF生成中，请在打印对话框中选择"另存为PDF"');
            }

            // 全屏预览功能
            function enterFullscreen(mode) {
                const fullscreenContainer = document.getElementById('fullscreenContainer');
                const fullscreenFrame = document.getElementById('fullscreenFrame');
                const fullscreenSvg = document.getElementById('fullscreenSvg');

                // 显示全屏容器
                fullscreenContainer.style.display = 'flex';

                if (mode === 'html' || mode === 'markdown') {
                    // 显示HTML/Markdown内容
                    fullscreenFrame.style.display = 'block';
                    fullscreenSvg.style.display = 'none';

                    // 复制当前预览内容
                    const currentDoc = previewFrame.contentDocument;
                    if (currentDoc) {
                        const newDoc = fullscreenFrame.contentDocument || fullscreenFrame.contentWindow.document;
                        newDoc.open();
                        newDoc.write(currentDoc.documentElement.outerHTML);
                        newDoc.close();
                    }
                } else if (mode === 'svg' || mode === 'mermaid') {
                    // 显示SVG/Mermaid内容
                    fullscreenFrame.style.display = 'none';
                    fullscreenSvg.style.display = 'flex';

                    // 复制SVG内容
                    const svgElement = svgContainer.querySelector('svg');
                    if (svgElement) {
                        fullscreenSvg.innerHTML = '';
                        fullscreenSvg.appendChild(svgElement.cloneNode(true));
                    }
                }

                // 阻止页面滚动
                document.body.style.overflow = 'hidden';
            }

            // 退出全屏预览
            function exitFullscreen() {
                const fullscreenContainer = document.getElementById('fullscreenContainer');
                fullscreenContainer.style.display = 'none';

                // 恢复页面滚动
                document.body.style.overflow = 'auto';

                // 清空内容
                document.getElementById('fullscreenFrame').srcdoc = '';
                document.getElementById('fullscreenSvg').innerHTML = '';
            }

            function captureAnimation(element) {
                console.log('开始捕获动画，元素:', element);
                const duration = 10000; // 增加捕获画的持续时间（毫秒）
                const fps = 20; // 增加每秒帧数以提高流畅度
                const frames = [];
                const frameInterval = 1000 / fps;
                let frameCount = Math.ceil(duration / frameInterval);

                // 显示进度条
                document.getElementById('gifProgress').style.display = 'block';
                const progressBar = document.getElementById('gifProgressBar');
                const progressText = document.getElementById('gifProgressText');

                let capturedFrames = 0;

                function captureFrame() {
                    console.log('正捕获帧...');
                    const targetElement = element.querySelector('body') || element; // 确保选择器有默认值
                    if (!targetElement) {
                        console.error('未找到目标元素');
                        return;
                    }
                    console.log('目标元素:', targetElement); // 调试输出
                    html2canvas(targetElement, {
                        scale: 2,
                        useCORS: true,
                        logging: true,
                        allowTaint: true,
                        backgroundColor: null
                    }).then(canvas => {
                        console.log('帧捕获成功');
                        frames.push(canvas.toDataURL('image/png'));
                        capturedFrames++;
                        const progress = Math.round((capturedFrames / frameCount) * 100);
                        progressBar.value = progress;
                        progressText.textContent = progress + '%';

                        if (frames.length < frameCount) {
                            setTimeout(captureFrame, frameInterval);
                        } else {
                            console.log('所有帧捕获完成，开始创建GIF');
                            createGif(frames, element);
                        }
                    }).catch(err => {
                        console.error('捕获帧失败:', err);
                        alert('捕获动画帧时出错，请检查您的代码和控制台日志错误: ' + err.message);
                        document.getElementById('gifProgress').style.display = 'none';
                    });
                }

                captureFrame();
            }

            function createGif(frames, element) {
                console.log('开始创建GIF，帧数:', frames.length);
                try {
                    if (typeof gifshot === 'undefined') {
                        throw new Error('gifshot 库未加载，请检查网络接或脚本引用。');
                    }

                    let contentWidth = element.scrollWidth;
                    let contentHeight = element.scrollHeight;

                    // 计算比例以保持原始宽高比
                    const aspectRatio = contentWidth / contentHeight;
                    const maxWidth = 800;
                    const maxHeight = 600;

                    if (contentWidth > maxWidth || contentHeight > maxHeight) {
                        if (contentWidth / maxWidth > contentHeight / maxHeight) {
                            contentWidth = maxWidth;
                            contentHeight = Math.floor(maxWidth / aspectRatio);
                        } else {
                            contentHeight = maxHeight;
                            contentWidth = Math.floor(maxHeight * aspectRatio);
                        }
                    }

                    console.log('GIF尺寸:', contentWidth, 'x', contentHeight);

                    // 查帧数据
                    if (frames.length === 0) {
                        throw new Error('没有捕获到帧');
                    }
                    console.log('第一帧数据:', frames[0].substring(0, 100) + '...');

                    // 显示进度条
                    document.getElementById('gifProgress').style.display = 'block';
                    const progressBar = document.getElementById('gifProgressBar');
                    const progressText = document.getElementById('gifProgressText');

                    gifshot.createGIF({
                        images: frames,
                        gifWidth: contentWidth,
                        gifHeight: contentHeight,
                        interval: 0.1,
                        numFrames: frames.length, // 确保使用所有帧
                        progressCallback: function(captureProgress) {
                            const progress = Math.round(captureProgress * 100);
                            console.log('GIF渲染进度:', progress + '%');
                            progressBar.value = progress;
                            progressText.textContent = progress + '%';
                        },
                    }, function(obj) {
                        document.getElementById('gifProgress').style.display = 'none';
                        document.getElementById('downloadGif').disabled = false;
                        if(!obj.error) {
                            const image = obj.image;
                            console.log('GIF渲染完成');
                            const link = document.createElement('a');
                            link.href = image;
                            let fileName = 'animation.gif';

                            // 取 SVG 或 HTML 的标题
                            if (currentMode === 'svg') {
                                const svgElement = svgContainer.querySelector('svg');
                                if (svgElement) {
                                    const titleElement = svgElement.querySelector('title');
                                    if (titleElement && titleElement.textContent) {
                                        fileName = titleElement.textContent.trim() + '.gif';
                                    } else {
                                        const h1Element = svgElement.querySelector('h1');
                                        if (h1Element && h1Element.textContent) {
                                            fileName = h1Element.textContent.trim() + '.gif';
                                        }
                                    }
                                }
                            } else if (currentMode === 'html') {
                                const doc = previewFrame.contentDocument;
                                const titleElement = doc.querySelector('title');
                                if (titleElement && titleElement.textContent) {
                                    fileName = titleElement.textContent.trim() + '.gif';
                                } else {
                                    const h1Element = doc.querySelector('h1');
                                    if (h1Element && h1Element.textContent) {
                                        fileName = h1Element.textContent.trim() + '.gif';
                                    }
                                }
                            }

                            link.download = fileName;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        } else {
                            console.error('创建GIF时发生错误:', obj.error);
                            console.error('错误详情:', JSON.stringify(obj, null, 2));
                            alert('创建GIF时发生错误: ' + obj.error + '\n请查看控制台以获取更多信息。');
                        }
                    });
                } catch (error) {
                    console.error('创建GIF时发生错:', error);
                    console.error('错误堆栈:', error.stack);
                    alert('创建GIF时发生错误: ' + error.message + '\n请查看控制台以获取更多���息。');
                    document.getElementById('gifProgress').style.display = 'none';
                    document.getElementById('downloadGif').disabled = false;
                }
            }

            // 只在测试环境中暴露函数
            if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') {
                window.detectCodeType = detectCodeType;
                window.getShortTimestamp = getShortTimestamp;
                window.showDefaultMessage = showDefaultMessage;
                window.switchMode = switchMode;
                window.generateFileName = generateFileName;
            }
        </script>
    </body>
    </html>