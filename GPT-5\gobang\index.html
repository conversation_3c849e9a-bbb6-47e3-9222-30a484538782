<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>五子棋 | GPT‑5</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap" rel="stylesheet">
  <link href="./styles.css" rel="stylesheet" />
</head>
<body>
  <div class="app">
    <header class="topbar">
      <div class="brand"><span class="spark"></span> Gobang</div>
    </header>

    <main class="main-centered">
      <section class="board-wrap board-zen">
        <div class="overlay-info" id="gameInfo"></div>
        <canvas id="board" width="640" height="640" aria-label="五子棋棋盘" role="img"></canvas>
      </section>
    </main>

    <div class="dock" role="toolbar" aria-label="控制条">
      <div class="dock-left">
        <button id="newGame" class="btn">新对局</button>
        <button id="undo" class="btn">悔棋</button>
        <button id="redo" class="btn" disabled>重做</button>
      </div>
      <div class="dock-right" id="controls">
        <div class="dock-seg">
          <span>模式</span>
          <select id="mode">
            <option value="pvp">人人</option>
            <option value="ai" selected>人机</option>
          </select>
        </div>
        <div class="dock-seg">
          <span>先手</span>
          <select id="first">
            <option value="black" selected>黑棋</option>
            <option value="white">白棋</option>
          </select>
        </div>
        <div class="dock-seg">
          <span>难度</span>
          <select id="level">
            <option value="1">入门</option>
            <option value="2" selected>标准</option>
            <option value="3">高手</option>
          </select>
        </div>
        <label class="dock-check">
          <input type="checkbox" id="sound" checked /> 声音
        </label>
      </div>
    </div>

    <footer class="footer">Designed with simplicity — GPT‑5</footer>
  </div>

  <script type="module" src="./app.js"></script>
</body>
</html>
