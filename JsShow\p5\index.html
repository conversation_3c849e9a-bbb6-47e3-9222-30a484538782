<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>p5.js - 交互粒子流</title>
  <style>
    html, body { margin: 0; height: 100%; background: #0b0e14; color: #e6e6e6; font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial; }
    #ui { position: fixed; top: 12px; left: 12px; background: rgba(0,0,0,.35); border: 1px solid rgba(255,255,255,.12); border-radius: 10px; padding: 10px 12px; font-size: 13px; }
    a { color: #5dd3fb; text-decoration: none; }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/p5@1.9.3/lib/p5.min.js"></script>
</head>
<body>
  <div id="ui">p5.js 交互粒子流 · 鼠标吸引 | <a href="../index.html">返回</a></div>
  <script>
    const particles = [];
    const NUM = 500;
    let noiseZ = 0;

    class Particle {
      constructor() {
        this.reset();
      }
      reset() {
        this.x = Math.random() * window.innerWidth;
        this.y = Math.random() * window.innerHeight;
        this.vx = 0; this.vy = 0;
        this.size = 2 + Math.random() * 2;
        this.hue = 180 + Math.random() * 120;
      }
      update(mx, my) {
        const angle = noise(this.x * 0.002, this.y * 0.002, noiseZ) * Math.PI * 4;
        const flowVx = Math.cos(angle) * 0.6;
        const flowVy = Math.sin(angle) * 0.6;

        const dx = mx - this.x; const dy = my - this.y;
        const dist2 = dx*dx + dy*dy;
        const influence = Math.min(12000 / (dist2 + 200), 1.8);
        this.vx += flowVx + dx * 0.0008 * influence;
        this.vy += flowVy + dy * 0.0008 * influence;

        this.vx *= 0.97; this.vy *= 0.97;
        this.x += this.vx; this.y += this.vy;

        if (this.x < -50 || this.x > width + 50 || this.y < -50 || this.y > height + 50) this.reset();
      }
      draw() {
        stroke(`hsla(${this.hue}, 80%, 65%, 0.5)`);
        strokeWeight(this.size);
        point(this.x, this.y);
      }
    }

    let mx = 0, my = 0;
    function setup() {
      createCanvas(window.innerWidth, window.innerHeight);
      pixelDensity(window.devicePixelRatio || 1);
      for (let i = 0; i < NUM; i++) particles.push(new Particle());
      background('#0b0e14');
    }

    function draw() {
      noFill();
      stroke(11,14,20, 20);
      rect(0,0,width,height);
      blendMode(ADD);
      for (const p of particles) { p.update(mx, my); p.draw(); }
      blendMode(BLEND);
      noiseZ += 0.003;
    }

    function mouseMoved() { mx = mouseX; my = mouseY; }
    function mouseDragged() { mx = mouseX; my = mouseY; }
    function windowResized() { resizeCanvas(window.innerWidth, window.innerHeight); background('#0b0e14'); }
  </script>
</body>
</html>
