// 设置图表尺寸和边距
const margin = {top: 20, right: 30, bottom: 40, left: 40};
const width = 800 - margin.left - margin.right;
const height = 400 - margin.top - margin.bottom;

// 创建SVG元素
const svg = d3.select("#chart")
    .append("svg")
    .attr("width", width + margin.left + margin.right)
    .attr("height", height + margin.top + margin.bottom)
    .append("g")
    .attr("transform", `translate(${margin.left},${margin.top})`);

// 创建初始数据
let data = Array.from({length: 10}, () => Math.floor(Math.random() * 100));

// 设置x和y比例尺
const xScale = d3.scaleBand()
    .domain(d3.range(data.length))
    .range([0, width])
    .padding(0.1);

const yScale = d3.scaleLinear()
    .domain([0, 100])
    .range([height, 0]);

// 创建坐标轴
const xAxis = d3.axisBottom(xScale);
const yAxis = d3.axisLeft(yScale);

// 添加x轴
svg.append("g")
    .attr("class", "axis")
    .attr("transform", `translate(0,${height})`)
    .call(xAxis);

// 添加y轴
svg.append("g")
    .attr("class", "axis")
    .call(yAxis);

// 添加条形
const bars = svg.selectAll(".bar")
    .data(data)
    .enter()
    .append("rect")
    .attr("class", "bar")
    .attr("x", (d, i) => xScale(i))
    .attr("y", d => yScale(d))
    .attr("width", xScale.bandwidth())
    .attr("height", d => height - yScale(d));

// 添加y轴标签
svg.append("text")
    .attr("class", "axis-label")
    .attr("transform", "rotate(-90)")
    .attr("y", 0 - margin.left)
    .attr("x", 0 - (height / 2))
    .attr("dy", "1em")
    .style("text-anchor", "middle")
    .text("数值");

// 更新数据的函数
function updateBars() {
    // 生成新数据
    data = data.map(() => Math.floor(Math.random() * 100));
    
    // 更新y比例尺
    yScale.domain([0, d3.max(data)]);
    
    // 更新条形
    bars.data(data)
        .transition()
        .duration(1000)
        .attr("y", d => yScale(d))
        .attr("height", d => height - yScale(d));
    
    // 更新y轴
    svg.select(".axis")
        .transition()
        .duration(1000)
        .call(yAxis);
}

// 每2秒更新一次数据
setInterval(updateBars, 2000);