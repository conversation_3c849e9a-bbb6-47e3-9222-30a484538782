export class CanvasBoard {
  constructor(canvas, size) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.size = size;
    this.padding = 24;
    this.cell = (canvas.width - this.padding * 2) / (size - 1);
    this.clickHandler = null;

    canvas.style.cursor = 'pointer';
    const mapEventToGrid = (clientX, clientY) => {
      const rect = canvas.getBoundingClientRect();
      const localX = clientX - rect.left; // CSS 像素
      const localY = clientY - rect.top;
      const x = Math.round((localX - this.padding) / this.cell);
      const y = Math.round((localY - this.padding) / this.cell);
      return { x, y };
    };

    canvas.addEventListener('click', (e) => {
      const { x, y } = mapEventToGrid(e.clientX, e.clientY);
      if (x >= 0 && y >= 0 && x < this.size && y < this.size) {
        this.clickHandler && this.clickHandler(x, y);
      }
    });

    canvas.addEventListener('touchstart', (e) => {
      const t = e.changedTouches[0];
      const { x, y } = mapEventToGrid(t.clientX, t.clientY);
      if (x >= 0 && y >= 0 && x < this.size && y < this.size) {
        this.clickHandler && this.clickHandler(x, y);
      }
    }, { passive: true });

    // 支持高分屏
    const ratio = window.devicePixelRatio || 1;
    // 统一用 CSS 像素计算逻辑，缩放仅用于像素密度
    const cssWidth = canvas.width;
    const cssHeight = canvas.height;
    if (ratio !== 1) {
      canvas.width = cssWidth * ratio;
      canvas.height = cssHeight * ratio;
      canvas.style.width = cssWidth + 'px';
      canvas.style.height = cssHeight + 'px';
      this.ctx.scale(ratio, ratio);
    }
    this.cell = (cssWidth - this.padding * 2) / (size - 1);
  }

  onClick(handler) { this.clickHandler = handler; }

  render(boardState, { lastMove, winnerLine }) {
    const ctx = this.ctx;
    const w = this.canvas.width / (window.devicePixelRatio || 1);
    const h = this.canvas.height / (window.devicePixelRatio || 1);
    ctx.clearRect(0, 0, w, h);

    this._drawBoard();
    this._drawPieces(boardState);
    if (winnerLine) this._highlightLine(winnerLine);
    if (lastMove) this._highlightLast(lastMove);
  }

  _drawBoard() {
    const ctx = this.ctx;
    const size = this.size;
    const pad = this.padding;
    const cell = this.cell;
    const ratio = window.devicePixelRatio || 1;
    const w = this.canvas.width / ratio;
    const h = this.canvas.height / ratio;

    // 背景
    const grad = ctx.createRadialGradient(w*0.5, h*0.15, 40, w*0.5, h*0.15, w*0.9);
    grad.addColorStop(0, 'rgba(134,239,172,0.08)');
    grad.addColorStop(1, 'rgba(0,0,0,0)');
    ctx.fillStyle = '#0b1119';
    ctx.fillRect(0, 0, w, h);
    ctx.fillStyle = grad;
    ctx.fillRect(0, 0, w, h);

    // 网格
    ctx.strokeStyle = 'rgba(255,255,255,0.10)';
    ctx.lineWidth = 1;
    for (let i = 0; i < size; i++) {
      const x = pad + i * cell;
      const y = pad + i * cell;
      ctx.beginPath();
      ctx.moveTo(pad, y); ctx.lineTo(pad + (size - 1) * cell, y); ctx.stroke();
      ctx.beginPath();
      ctx.moveTo(x, pad); ctx.lineTo(x, pad + (size - 1) * cell); ctx.stroke();
    }

    // 星位
    const stars = [3, 7, 11];
    ctx.fillStyle = 'rgba(255,255,255,0.6)';
    for (const sy of stars) {
      for (const sx of stars) {
        ctx.beginPath();
        ctx.arc(pad + sx * cell, pad + sy * cell, 3, 0, Math.PI * 2);
        ctx.fill();
      }
    }
  }

  _drawPieces(boardState) {
    const ctx = this.ctx;
    const pad = this.padding; const cell = this.cell;
    for (let y = 0; y < boardState.size; y++) {
      for (let x = 0; x < boardState.size; x++) {
        const v = boardState.get(x, y);
        if (v === 0) continue;
        const cx = pad + x * cell; const cy = pad + y * cell;
        const r = cell * 0.37;
        // 外圈柔和阴影
        ctx.save();
        ctx.shadowColor = 'rgba(0,0,0,0.35)';
        ctx.shadowBlur = 10;
        ctx.shadowOffsetY = 2;
        ctx.beginPath();
        const grad = ctx.createRadialGradient(cx - r * 0.35, cy - r * 0.35, r * 0.2, cx, cy, r);
        if (v === 1) { grad.addColorStop(0, '#5b5f66'); grad.addColorStop(1, '#0a0b0d'); }
        else { grad.addColorStop(0, '#ffffff'); grad.addColorStop(1, '#d8dbe2'); }
        ctx.fillStyle = grad;
        ctx.arc(cx, cy, r, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
        // 顶部高光
        ctx.beginPath();
        const hlr = r * 0.5;
        const hl = ctx.createRadialGradient(cx - r * 0.35, cy - r * 0.35, 0, cx - r * 0.35, cy - r * 0.35, hlr);
        hl.addColorStop(0, 'rgba(255,255,255,0.55)');
        hl.addColorStop(1, 'rgba(255,255,255,0)');
        ctx.fillStyle = hl;
        ctx.arc(cx - r * 0.15, cy - r * 0.18, hlr, 0, Math.PI * 2);
        ctx.fill();
      }
    }
  }

  _highlightLast({ x, y, color }) {
    const ctx = this.ctx;
    const pad = this.padding; const cell = this.cell;
    const cx = pad + x * cell; const cy = pad + y * cell; const r = cell * 0.42;
    ctx.strokeStyle = color === 'black' ? '#72f1b8' : '#6aa1ff';
    ctx.lineWidth = 2;
    ctx.beginPath(); ctx.arc(cx, cy, r, 0, Math.PI * 2); ctx.stroke();
  }

  _highlightLine(coords) {
    const ctx = this.ctx;
    const pad = this.padding; const cell = this.cell;
    ctx.strokeStyle = '#f59e0b';
    ctx.lineWidth = 4;
    ctx.beginPath();
    for (let i = 0; i < coords.length; i++) {
      const p = coords[i];
      const cx = pad + p.x * cell; const cy = pad + p.y * cell;
      if (i === 0) ctx.moveTo(cx, cy); else ctx.lineTo(cx, cy);
    }
    ctx.stroke();
  }
}
