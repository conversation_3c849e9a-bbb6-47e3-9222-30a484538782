<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>D3.js - 过渡柱状图</title>
  <style>
    html, body { margin: 0; height: 100%; background: #0b0e14; color: #e6e6e6; font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial; }
    #wrap { max-width: 960px; margin: 40px auto; padding: 0 16px; }
    #ui { color: #9aa4b2; margin-bottom: 10px; font-size: 13px; }
    button { background: #1a2333; color: #e6e6e6; border: 1px solid rgba(255,255,255,.14); border-radius: 10px; padding: 8px 12px; margin-right: 8px; cursor: pointer; }
    button:hover { border-color: #5dd3fb; }
    a { color: #5dd3fb; text-decoration: none; }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/d3@7/dist/d3.min.js"></script>
</head>
<body>
  <div id="wrap">
    <div id="ui">D3.js 过渡柱状图 | <button id="shuffle">随机数据</button> <button id="sort">排序</button> | <a href="../index.html">返回</a></div>
    <svg id="chart" width="960" height="420"></svg>
  </div>

  <script>
    const svg = d3.select('#chart');
    const width = +svg.attr('width');
    const height = +svg.attr('height');
    const margin = { top: 24, right: 24, bottom: 40, left: 40 };
    const innerW = width - margin.left - margin.right;
    const innerH = height - margin.top - margin.bottom;

    const g = svg.append('g').attr('transform', `translate(${margin.left},${margin.top})`);

    let data = d3.range(12).map(() => Math.round(Math.random() * 100 + 10));
    let sortAsc = false;

    const x = d3.scaleBand().domain(d3.range(data.length)).range([0, innerW]).padding(0.16);
    const y = d3.scaleLinear().domain([0, d3.max(data)]).range([innerH, 0]).nice();

    const xAxis = g.append('g').attr('transform', `translate(0,${innerH})`).call(d3.axisBottom(x).tickFormat(i => i + 1).tickSizeOuter(0));
    const yAxis = g.append('g').call(d3.axisLeft(y));

    const bars = g.append('g').attr('fill', '#5dd3fb');

    function render() {
      y.domain([0, d3.max(data)]).nice();
      yAxis.transition().duration(600).call(d3.axisLeft(y));

      const sel = bars.selectAll('rect').data(data, (d, i) => i);

      sel.join(
        enter => enter.append('rect')
          .attr('x', (_, i) => x(i))
          .attr('y', innerH)
          .attr('width', x.bandwidth())
          .attr('height', 0)
          .attr('rx', 4)
          .transition()
          .duration(800)
          .delay((_, i) => i * 30)
          .attr('y', d => y(d))
          .attr('height', d => innerH - y(d)),
        update => update
          .transition()
          .duration(700)
          .attr('x', (_, i) => x(i))
          .attr('y', d => y(d))
          .attr('height', d => innerH - y(d))
          .attr('width', x.bandwidth()),
        exit => exit
          .transition()
          .duration(500)
          .attr('y', innerH)
          .attr('height', 0)
          .remove()
      );
    }

    render();

    document.getElementById('shuffle').addEventListener('click', () => {
      data = d3.range(12).map(() => Math.round(Math.random() * 100 + 10));
      if (sortAsc) data.sort(d3.ascending);
      render();
    });

    document.getElementById('sort').addEventListener('click', () => {
      sortAsc = !sortAsc;
      const order = d3.range(data.length).sort((a, b) => sortAsc ? d3.ascending(data[a], data[b]) : d3.descending(data[a], data[b]));
      x.domain(order);

      bars.selectAll('rect')
        .transition()
        .duration(800)
        .delay((_, i) => i * 20)
        .attr('x', (_, i) => x(i));

      xAxis.transition().duration(800).call(d3.axisBottom(x).tickFormat(i => i + 1).tickSizeOuter(0));
    });
  </script>
</body>
</html>
