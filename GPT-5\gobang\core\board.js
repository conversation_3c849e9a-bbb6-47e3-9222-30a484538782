export function createBoardState(size) {
  const cells = Array.from({ length: size }, () => Array(size).fill(0));
  return {
    size,
    cells,
    reset() {
      for (let i = 0; i < size; i++) cells[i].fill(0);
    },
    get(x, y) {
      if (x < 0 || y < 0 || x >= size || y >= size) return -1;
      return cells[y][x];
    },
    set(x, y, v) {
      if (x < 0 || y < 0 || x >= size || y >= size) return false;
      cells[y][x] = v; return true;
    },
    isFull() {
      for (let y = 0; y < size; y++) {
        for (let x = 0; x < size; x++) if (cells[y][x] === 0) return false;
      }
      return true;
    }
  };
}
