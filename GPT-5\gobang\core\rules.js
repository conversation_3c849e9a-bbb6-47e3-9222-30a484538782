export class Rules {
  checkWin(boardState, x, y) {
    const color = boardState.get(x, y);
    if (color <= 0) return null;
    const dirs = [ [1,0], [0,1], [1,1], [1,-1] ];
    for (const [dx, dy] of dirs) {
      const line = this._countLine(boardState, x, y, dx, dy, color);
      if (line.count >= 5) {
        return { color: color === 1 ? 'black' : 'white', line: line.coords };
      }
    }
    return null;
  }

  _countLine(boardState, x, y, dx, dy, color) {
    let count = 1;
    const coords = [{ x, y }];

    // forward
    let i = 1;
    while (boardState.get(x + dx * i, y + dy * i) === color) {
      coords.push({ x: x + dx * i, y: y + dy * i });
      count++; i++;
    }
    // backward
    i = 1;
    while (boardState.get(x - dx * i, y - dy * i) === color) {
      coords.unshift({ x: x - dx * i, y: y - dy * i });
      count++; i++;
    }
    return { count, coords };
  }
}
