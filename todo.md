# DOMEditPlus — Markdown 支持实施计划

## 背景与目标
在现有支持 HTML / SVG / Mermaid 的所见即所得编辑器基础上，新增 Markdown 源码处理与预览能力，保证与现有导出（下载源码、下载 PNG、复制 PNG、下载 GIF）功能一致工作，并与自动模式协同运作。

---

## 现有代码结构（简要摘要）
- 双栏布局：左侧 `textarea#codeTextarea`，右侧预览区（`iframe#previewFrame` + `div#svgContainer`）。
- 模式切换：`#modeSwitch`（auto/html/svg/mermaid），`switchMode(mode)` 控制展示容器与渲染路径。
- 内容识别：`detectCodeType(code)` 用启发式判断 html/svg/mermaid。
- 预览渲染：`updatePreview()` 根据 `currentMode` 分支：
  - html → 向 `iframe` 写入 `srcdoc`（`document.write`）。
  - svg → `DOMParser` 解析后安全裁剪并注入 `#svgContainer`。
  - mermaid → 写入 `<pre.mermaid>`，调用 `mermaid.render`，再把 svg 注入。
- 导出：
  - 代码下载：根据模式命名（`generateFileName`），对 HTML/SVG 还会尝试基于文档首个文本内容生成语义化文件名。
  - PNG：根据模式分流到 `handleHtmlToPng` / `handleSvgToPng` / `handleMermaidToPng`，命名通过 `generateImageFileName`。
  - 复制 PNG：与下载 PNG 同源，最后通过 `handleCanvasToClipboard`。
  - GIF：HTML 走 `html2canvas` 多帧抓取，SVG 走 `svg` 路径；Mermaid 尚未接入 GIF（但可复用 SVG 或 HTML 路径）。
- 安全与体验：
  - SVG 有 `sanitizeSvgElement` 去除脚本/事件/可疑链接。
  - 预览 `iframe` 使用 `sandbox="allow-scripts allow-same-origin"`。
  - 输入防抖 300ms，分隔条可拖拽/键盘可达，下载/复制有通知。

---

## Markdown 集成评估
- 结构适配：
  - 与 HTML 渲染路径最相近：Markdown → HTML → `iframe` 预览 → 导出 PNG/GIF 复用 HTML 截图路径。
- 冲突/协同：
  - 协同：PNG/复制/GIF 的 HTML 截图流程可直接复用；自动模式可扩展 `detectCodeType` 增加 `markdown` 识别分支。
  - 潜在冲突：Markdown 支持内嵌原生 HTML，需防 XSS；与 Mermaid 同时使用时，可在 Markdown 代码围栏 ```mermaid 中自动渲染。
- 性能：
  - 渲染在 300ms 防抖后执行；`marked` 解析复杂度线性，足够；可按需只在 Markdown 模式加载高亮样式。
- 用户体验：
  - 在模式下拉中新增 “Markdown 模式”；自动模式自动识别 md。
  - 代码高亮与表格样式注入到 `iframe`，保证视觉一致性；Mermaid 围栏自动渲染。

---

## 支持的 Markdown 特性（首批）
- 标题（# ~ ######）
- 段落、换行、引用
- 有序/无序列表、任务列表
- 粗体/斜体/删除线/行内代码
- 链接、图片
- 代码块（围栏 ```）+ 语法高亮（highlight.js 自动/显式语言）
- 表格（GFM）
- 水平分割线
- 公式（可暂不内置渲染，仅作为代码/文本展示；可选后续集成 KaTeX）
- Mermaid：```mermaid 围栏自动渲染为 SVG（延续现有 mermaid 依赖）

---

## 技术路线
- 解析与渲染：`marked`（或 `markdown-it`，二者均可；本方案选 `marked`）
- 安全：`DOMPurify` 对 Markdown 渲染结果进行净化（保留基本标签、表格、代码块）
- 代码高亮：`highlight.js`（`marked` 的 `highlight` 回调中调用 `hljs`，并向 `iframe` 注入对应 CSS 主题）
- Mermaid：在 Markdown 渲染完成后，查找 `pre > code.language-mermaid` 或自定义占位，使用现有 `mermaid.render` 将其替换为内联 SVG

---

## 集成点与变更清单
- UI：
  - 下拉框 `#modeSwitch` 新增 `<option value="markdown">Markdown 模式</option>`。
- 识别：
  - `detectCodeType` 新增 `markdown` 分支（通过多模式启发式：标题、围栏、列表、表格、链接语法等），且不与已识别的 html/svg/mermaid 冲突。
- 预览：
  - `updatePreview` 新增 `markdown` 路径：
    1) 使用 `marked.parse(code, {highlight})` → `html`
    2) `DOMPurify.sanitize(html, {ALLOWED_TAGS/ATTR})`
    3) 写入 `iframe` 文档（包含基本样式与 `highlight.js` CSS link）
    4) 在 `iframe` 文档内查找 mermaid 围栏，调用 `mermaid.render` 注入 SVG
- 导出：
  - `generateFileName`：`markdown` → `.md`
  - PNG/复制 PNG：走 HTML 截图路径（与 `html` 一致）
  - GIF：同 HTML 路径
- 依赖：
  - 通过 CDN 注入：
    - marked（稳定版）
    - DOMPurify
    - highlight.js 核心与主题 CSS（例如 github 样式）

---

## UI/UX 细节
- 预览主题：向 `iframe` 注入基础排版与表格样式，保证良好可读性。
- 代码块：注入 `highlight.js` 主题 CSS（只影响 `iframe`）。
- Mermaid：围栏自动渲染，渲染失败时显示错误消息，不影响其他内容。
- 默认提示：当 Markdown 模式且空内容时提示 “请在左侧输入有效的 Markdown 源码”。

---

## 测试策略
- 手动用例（关键路径）：
  - 基础语法：标题/列表/链接/图片/表格/代码块/分割线/引用
  - 代码高亮：无语言/显式语言两种
  - Mermaid 围栏：简单 graph/sequence/mindmap 用例
  - 自动模式识别：粘贴 html/svg/mermaid/md 各类文本
  - 导出：
    - 下载源码：`.md` 命名
    - 下载/复制 PNG：对齐 HTML 路径
    - 下载 GIF：对齐 HTML 路径
  - 回归：原 HTML/SVG/Mermaid 模式均正常
- 边界：超长文档、包含原生 HTML 的 Markdown、错误的 mermaid 代码、无网络/依赖加载失败

---

## 待办清单（执行时逐项打勾）
- [x] 新增依赖脚本与样式（marked、DOMPurify、highlight.js 及主题 CSS）
- [x] UI：`#modeSwitch` 新增 `markdown` 选项
- [x] 识别：扩展 `detectCodeType` 支持 `markdown`
- [x] 预览：实现 Markdown → HTML → `iframe` 渲染流程（含 sanitize & highlight）
- [x] 预览：在 `iframe` 内渲染 ```mermaid 围栏为 SVG
- [x] 导出：`generateFileName` 支持 `.md`
- [x] 导出：PNG/复制 PNG/GIF 支持 `markdown`（复用 HTML 流程）
- [x] 空态与错误提示完善（Markdown 模式）
- [ ] 回归测试与手动验收
\- [x] UI：导出按钮区域默认隐藏，悬停/聚焦时显示（触屏始终显示）
\- [x] UI：模式下拉框默认隐藏，悬停/聚焦时显示（触屏始终显示）
\- [x] 交互：分隔条双击复位为 50/50，宽度写入本地存储
\- [x] 交互：记忆模式、主题与编辑内容（本地存储）
\- [x] 主题：新增主题切换按钮，Markdown/mermaid 深浅色联动
\- [x] 性能：按需加载 Markdown 依赖（marked/DOMPurify/highlight.js），失败时降级为纯文本

---

## 变更影响与回滚
- 影响面：`DOMEditPlus.html` 单文件内功能扩展，低耦合、易回滚。
- 回滚策略：移除新增依赖与 `markdown` 分支即可恢复原行为。

---

## 审查区（实施完成后补充）
- 实施摘要：在 `DOMEditPlus.html` 中引入 marked、DOMPurify、highlight.js；扩展模式识别与切换；在 `updatePreview` 中新增 Markdown 渲染与净化，并在 `iframe` 内将 ```mermaid 围栏渲染为 SVG；导出与命名逻辑覆盖 Markdown；空态提示覆盖 Markdown；GIF/PNG/复制流程复用 HTML 路径。
- 关键代码位置：
  - 依赖引入：`<script src="...marked...">`、`...dompurify...`、`...highlight.js...`
  - 识别：`detectCodeType` 新增 Markdown 启发式
  - 渲染：`updatePreview` 的 `markdown` 分支
  - 命名：`generateFileName` 与下载事件监听器处
  - 空态：`showDefaultMessage()` 支持 `markdown`
- 已知限制：
  - `iframe` 内未引入 highlight.js 的脚本，仅使用预计算后的 HTML + CSS；若需在运行时对动态注入代码块再高亮，可考虑在 `iframe` 里执行 `hljs.highlightAll()`。
  - Markdown 中的原生 HTML 允许范围由 DOMPurify 决定，当前仅添加 `class` 属性白名单。
  - 数学公式（LaTeX）暂未渲染；后续可选集成 KaTeX。
- 后续建议：
  - 增加 `katex` 以支持 `$...$`/`$$...$$` 公式渲染；
  - 允许用户选择 highlight.js 主题；
  - 支持粘贴图片并以 data URL 或上传接口内联。
