<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>GPT-5 | 核心能力</title>
  <meta name="description" content="GPT-5 的核心能力与特性简介" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap" rel="stylesheet">
  <style>
    :root {
      --bg: #0b1020;
      --card: #11182e;
      --text: #e6e9f2;
      --muted: #aab2c5;
      --brand: #6aa1ff;
      --accent: #72f1b8;
      --ring: rgba(114, 241, 184, 0.35);
      --grid: rgba(255, 255, 255, 0.06);
    }
    * { box-sizing: border-box; }
    html, body { height: 100%; }
    body {
      margin: 0;
      font-family: 'Inter', system-ui, -apple-system, Segoe UI, Roboto, PingFang SC, Noto Sans SC, Helvetica, Arial, sans-serif;
      background: radial-gradient(1200px 800px at 80% -10%, rgba(114,241,184,0.12), transparent),
                  radial-gradient(900px 600px at 10% 0%, rgba(106,161,255,0.12), transparent),
                  var(--bg);
      color: var(--text);
    }
    .container { max-width: 1080px; margin: 0 auto; padding: 40px 24px 80px; }
    header { display: flex; align-items: center; justify-content: space-between; gap: 16px; }
    .logo {
      display: flex; align-items: center; gap: 12px;
      font-weight: 800; letter-spacing: 0.3px; font-size: 18px;
    }
    .spark { width: 12px; height: 12px; background: linear-gradient(135deg, var(--accent), var(--brand)); border-radius: 3px; box-shadow: 0 0 0 6px var(--ring), 0 0 24px var(--accent); }
    .tag { padding: 6px 10px; border: 1px solid var(--grid); border-radius: 8px; color: var(--muted); font-size: 12px; }

    .hero { margin-top: 48px; display: grid; grid-template-columns: 1.2fr 0.8fr; gap: 24px; align-items: center; }
    .title { font-size: 48px; line-height: 1.1; margin: 0; }
    .subtitle { color: var(--muted); margin: 12px 0 24px; font-size: 18px; }

    .cta {
      display: inline-flex; align-items: center; gap: 10px; padding: 12px 16px; border-radius: 12px; border: 1px solid var(--grid);
      color: var(--text); text-decoration: none; background: linear-gradient(180deg, rgba(114,241,184,0.12), rgba(114,241,184,0.04));
      transition: transform .15s ease, box-shadow .15s ease, border-color .2s ease;
    }
    .cta:hover { transform: translateY(-1px); box-shadow: 0 8px 24px rgba(0,0,0,0.25); border-color: var(--accent); }

    .card { background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.00)); border: 1px solid var(--grid); border-radius: 16px; padding: 20px; }

    .grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; margin-top: 24px; }
    .grid .card { min-height: 160px; }
    .card h3 { margin: 0 0 8px; font-size: 16px; }
    .card p { margin: 0; color: var(--muted); line-height: 1.6; }

    .section { margin-top: 48px; }
    .section h2 { font-size: 20px; margin: 0 0 12px; }

    .code {
      font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", monospace;
      background: #0a0f1f; border: 1px solid var(--grid); border-radius: 10px; padding: 12px; color: #b8c4ff; overflow-x: auto;
    }

    footer { margin-top: 64px; color: var(--muted); font-size: 12px; }

    @media (max-width: 880px) {
      .hero { grid-template-columns: 1fr; }
      .title { font-size: 36px; }
      .grid { grid-template-columns: 1fr; }
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <div class="logo"><span class="spark"></span> GPT‑5</div>
      <div class="tag">模型级 AI 开发助手</div>
    </header>

    <section class="hero">
      <div>
        <h1 class="title">更强上下文，更稳执行，更懂代码</h1>
        <p class="subtitle">我是 GPT‑5 —— 面向工程与创作的多模态智能体。具备更长上下文、可靠工具使用、并行检索与编辑能力，专为构建、调试与发布而设计。</p>
        <a class="cta" href="#capabilities">查看核心能力</a>
      </div>
      <div class="card">
        <h3>即时特性</h3>
        <ul>
          <li>最长上下文与稳健对话记忆</li>
          <li>结构化思维与可控输出</li>
          <li>高容错工具编排与并行执行</li>
          <li>代码级读写、重构与测试生成</li>
        </ul>
      </div>
    </section>

    <section class="section" id="capabilities">
      <h2>核心能力</h2>
      <div class="grid">
        <div class="card">
          <h3>代码理解与重构</h3>
          <p>使用并行检索与上下文聚合理解大型代码库；生成可读性高、边界场景完备的重构方案，并保持风格一致。</p>
        </div>
        <div class="card">
          <h3>多模态推理</h3>
          <p>支持文本、图像等多模态输入，进行跨模态对齐与推理，适用于产品设计、数据洞察与可视化理解。</p>
        </div>
        <div class="card">
          <h3>可靠工具调用</h3>
          <p>面向 Shell、Git、搜索与编辑工具的安全编排，避免长驻进程，保证可重复执行与可追踪性。</p>
        </div>
        <div class="card">
          <h3>测试与质量保障</h3>
          <p>自动补齐单元测试、端到端测试与覆盖率检测；在变更后触发构建并自愈常见失败。</p>
        </div>
        <div class="card">
          <h3>文档与知识库</h3>
          <p>从代码与变更历史中提炼 API 文档、设计说明与迁移指南；输出结构化、可索引的内容。</p>
        </div>
        <div class="card">
          <h3>代理化工作流</h3>
          <p>支持多步骤计划、状态更新与总结；在无人值守的前提下推进任务，必要时请求最小人类确认。</p>
        </div>
      </div>
    </section>

    <section class="section">
      <h2>示例：指令到变更</h2>
      <pre class="code"><code>用户：在 src 下新建 utils/date.ts 并实现 formatDate
助手：
1) 新建文件，写入实现
2) 生成单测并运行
3) 构建并修复错误
4) 产出总结与后续建议</code></pre>
    </section>

    <section class="section">
      <h2>使用建议</h2>
      <ul>
        <li>给出明确目标与约束（性能、可维护性、兼容性）</li>
        <li>提供现有代码或数据样本以便快速对齐</li>
        <li>允许自动化工具调用并查看每次更改摘要</li>
      </ul>
    </section>

    <footer>
      <div>© GPT‑5 · 构建者模式</div>
    </footer>
  </div>
</body>
</html>
