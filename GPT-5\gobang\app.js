import { createBoardState } from './core/board.js';
import { createHistory } from './core/history.js';
import { Rules } from './core/rules.js';
import { CanvasBoard } from './ui/canvasBoard.js';
// Controls UI is defined in HTML; no dynamic mount needed
import { Sound } from './ui/sound.js';
import { findBestMove } from './ai/search.js';

const GAME_SIZE = 15;

const state = {
  mode: 'ai', // 'ai' | 'pvp'
  first: 'black', // 'black' | 'white'
  level: 2, // 1-3
  sound: true,
  current: 'black',
  winner: null,
  aiThinking: false,
};

const boardState = createBoardState(GAME_SIZE);
const history = createHistory();
const rules = new Rules();
const canvas = new CanvasBoard(document.getElementById('board'), GAME_SIZE);
const sound = new Sound();
// const controls = new Controls(document.getElementById('controls'));

let aiWorker = null;

function updateInfo() {
  const el = document.getElementById('gameInfo');
  const text = state.winner === 'draw'
    ? '平局'
    : state.winner
      ? `胜者：${state.winner === 'black' ? '黑棋' : '白棋'}`
      : state.aiThinking
        ? 'AI 思考中...'
        : `当前：${state.current === 'black' ? '黑棋' : '白棋'}`;
  el.textContent = text;
}

function resetGame() {
  boardState.reset();
  history.reset();
  state.current = state.first;
  state.winner = null;
  state.aiThinking = false;
  canvas.render(boardState, { lastMove: null, winnerLine: null });
  updateInfo();
  if (state.mode === 'ai' && state.first === 'white') {
    requestAIMove();
  }
}

function toggleTurn() {
  state.current = state.current === 'black' ? 'white' : 'black';
}

function handleMove(x, y) {
  if (state.winner || state.aiThinking) return;
  if (boardState.get(x, y) !== 0) return;
  const color = state.current;
  boardState.set(x, y, color === 'black' ? 1 : 2);
  history.push({ x, y, color });
  sound.place();

  const win = rules.checkWin(boardState, x, y);
  const draw = boardState.isFull();
  canvas.render(boardState, { lastMove: { x, y, color }, winnerLine: win?.line || null });

  if (win) {
    state.winner = color;
    sound.win();
    updateInfo();
    return;
  }
  if (draw) {
    state.winner = 'draw';
    updateInfo();
    return;
  }

  toggleTurn();
  updateInfo();

  if (state.mode === 'ai' && state.current !== state.first) {
    requestAIMove();
  }
}

function undo() {
  if (state.aiThinking) return;
  const step1 = history.pop();
  if (!step1) return;
  boardState.set(step1.x, step1.y, 0);
  toggleTurn();

  // 如果是人机模式，再撤一步让到玩家回合
  if (state.mode === 'ai') {
    const step2 = history.pop();
    if (step2) {
      boardState.set(step2.x, step2.y, 0);
      toggleTurn();
    }
  }
  state.winner = null;
  canvas.render(boardState, { lastMove: null, winnerLine: null });
  updateInfo();
}

function redo() {
  // 简化：不实现重做栈，保留接口展示。可扩展 createHistory 支持 redo。
}

function setupAI() {
  if (aiWorker) aiWorker.terminate();
  try {
    aiWorker = new Worker('./ai/worker.js', { type: 'module' });
    aiWorker.onmessage = (e) => {
      const { x, y } = e.data;
      state.aiThinking = false;
      handleMove(x, y);
    };
    aiWorker.onerror = () => {
      // 失败则回退到主线程搜索
      aiWorker.terminate();
      aiWorker = null;
    };
  } catch (_) {
    aiWorker = null; // 例如在 file:// 场景下
  }
}

function requestAIMove() {
  if (!aiWorker) setupAI();
  state.aiThinking = true;
  updateInfo();

  const aiColor = state.current === 'black' ? 1 : 2;
  if (aiWorker) {
    aiWorker.postMessage({
      board: boardState.cells,
      size: boardState.size,
      level: state.level,
      aiColor,
    });
  } else {
    // 主线程回退，避免阻塞：轻量 setTimeout
    setTimeout(() => {
      const move = findBestMove(boardState.cells, boardState.size, state.level, aiColor);
      state.aiThinking = false;
      handleMove(move.x, move.y);
    }, 0);
  }
}

function bindUI() {
  canvas.onClick((x, y) => {
    if (state.mode === 'ai' && state.current !== state.first) return; // 等 AI 落子
    handleMove(x, y);
  });

  document.getElementById('newGame').addEventListener('click', resetGame);
  document.getElementById('undo').addEventListener('click', undo);
  document.getElementById('redo').addEventListener('click', redo);

  const mode = document.getElementById('mode');
  const first = document.getElementById('first');
  const level = document.getElementById('level');
  const soundToggle = document.getElementById('sound');

  mode.addEventListener('change', (e) => {
    state.mode = e.target.value;
    resetGame();
  });
  first.addEventListener('change', (e) => {
    state.first = e.target.value;
    resetGame();
  });
  level.addEventListener('change', (e) => {
    state.level = Number(e.target.value);
  });
  soundToggle.addEventListener('change', (e) => {
    state.sound = e.target.checked;
    sound.enabled = state.sound;
  });

  // Controls already present in DOM; no remount to preserve layout
}

function init() {
  setupAI();
  canvas.render(boardState, { lastMove: null, winnerLine: null });
  updateInfo();
  bindUI();
}

if (document.readyState === 'loading') {
  window.addEventListener('DOMContentLoaded', init);
} else {
  init();
}
