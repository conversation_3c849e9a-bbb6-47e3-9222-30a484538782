export class Controls {
  constructor(container) { this.container = container; }
  mount({ mode, first, level, soundToggle }) {
    this.container.innerHTML = '';
    const wrap = document.createElement('div');
    wrap.className = 'actions';
    wrap.append(this._seg('模式', mode), this._seg('先手', first), this._seg('难度', level), this._seg('声音', soundToggle));
    this.container.appendChild(wrap);
  }

  _seg(labelText, node) {
    const seg = document.createElement('div');
    seg.style.display = 'flex';
    seg.style.alignItems = 'center';
    seg.style.gap = '8px';
    const label = document.createElement('span');
    label.textContent = labelText;
    label.style.color = 'var(--muted)';
    seg.append(label, node);
    return seg;
  }
}
