const data = [12, 36, 20, 25, 30, 15];

const svg = d3.select("svg");
const width = svg.attr("width");
const height = svg.attr("height");
const margin = { top: 20, right: 20, bottom: 30, left: 40 };
const innerWidth = width - margin.left - margin.right;
const innerHeight = height - margin.top - margin.bottom;

const xScale = d3.scaleBand()
    .domain(data.map((d, i) => i))
    .range([0, innerWidth])
    .padding(0.1);

const yScale = d3.scaleLinear()
    .domain([0, d3.max(data)])
    .range([innerHeight, 0]);

const g = svg.append("g")
    .attr("transform", `translate(${margin.left},${margin.top})`);

g.append("g")
    .attr("class", "axis axis--x")
    .attr("transform", `translate(0,${innerHeight})`)
    .call(d3.axisBottom(xScale));

g.append("g")
    .attr("class", "axis axis--y")
    .call(d3.axisLeft(yScale).ticks(10))
    .append("text")
    .attr("class", "axis-label")
    .attr("transform", "rotate(-90)")
    .attr("y", 6)
    .attr("dy", "0.71em")
    .attr("text-anchor", "end")
    .text("Value");

g.selectAll(".bar")
    .data(data)
    .enter().append("rect")
    .attr("class", "bar")
    .attr("x", (d, i) => xScale(i))
    .attr("y", d => yScale(d))
    .attr("width", xScale.bandwidth())
    .attr("height", d => innerHeight - yScale(d));
