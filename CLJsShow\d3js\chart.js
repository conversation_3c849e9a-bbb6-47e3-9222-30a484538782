const data = [
    {name: "A", value: 10},
    {name: "B", value: 20},
    {name: "C", value: 15},
    {name: "D", value: 25},
    {name: "E", value: 18},
    {name: "F", value: 30}
];

const width = 500;
const height = 300;
const margin = {top: 20, right: 20, bottom: 30, left: 40};

const svg = d3.select(".chart")
    .attr("width", width)
    .attr("height", height);

const x = d3.scaleBand()
    .domain(data.map(d => d.name))
    .range([margin.left, width - margin.right])
    .padding(0.1);

const y = d3.scaleLinear()
    .domain([0, d3.max(data, d => d.value)])
    .nice()
    .range([height - margin.bottom, margin.top]);

const xAxis = g => g
    .attr("transform", `translate(0,${height - margin.bottom})`)
    .call(d3.axisBottom(x).tickSizeOuter(0));

const yAxis = g => g
    .attr("transform", `translate(${margin.left},0)`)
    .call(d3.axisLeft(y));

svg.append("g").call(xAxis);
svg.append("g").call(yAxis);

svg.append("g")
    .attr("class", "bars")
    .selectAll(".bar")
    .data(data)
    .join("rect")
    .attr("class", "bar")
    .attr("x", d => x(d.name))
    .attr("y", d => y(d.value))
    .attr("height", d => y(0) - y(d.value))
    .attr("width", x.bandwidth());
