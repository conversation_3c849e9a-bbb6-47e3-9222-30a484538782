<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Playwright & MCP 演示</title>
    <style>
        body { font-family: sans-serif; line-height: 1.6; padding: 2em; }
        button { padding: 0.5em 1em; font-size: 1em; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Playwright 与 MCP 服务辅助开发演示</h1>
    <p>这是一个简单的目标页面，我们将用 Playwright 操作它。</p>
    <div id="container">
        <input type="text" id="myInput" placeholder="在此输入文本...">
        <button id="myButton">提交</button>
    </div>
    <p id="message"></p>

    <script>
        document.getElementById('myButton').addEventListener('click', () => {
            const input = document.getElementById('myInput');
            const message = document.getElementById('message');
            message.textContent = `你输入了: "${input.value}"`;
        });
    </script>
</body>
</html>
