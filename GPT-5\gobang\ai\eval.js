export function generateCandidateMoves(board, size) {
  const moves = [];
  const inBound = (x,y)=> x>=0&&y>=0&&x<size&&y<size;
  const dirs = [[1,0],[0,1],[1,1],[1,-1]];
  const hasNeighbor = (x,y)=>{
    for (const [dx,dy] of dirs){
      for (let k=1;k<=2;k++){
        const nx=x+dx*k, ny=y+dy*k; if (inBound(nx,ny) && board[ny][nx]!==0) return true;
        const px=x-dx*k, py=y-dy*k; if (inBound(px,py) && board[py][px]!==0) return true;
      }
    }
    return false;
  };
  for (let y=0;y<size;y++){
    for (let x=0;x<size;x++){
      if (board[y][x]!==0) continue;
      if (hasNeighbor(x,y)) moves.push({x,y});
    }
  }
  if (moves.length===0) moves.push({x:Math.floor(size/2), y:Math.floor(size/2)});
  return moves;
}

export function evaluateBoard(board, size, colorToMove) {
  // 朴素形势评估：统计开放三/四与活二，黑正、白负
  const scoreLines = (line) => {
    const s = line.join('');
    let score = 0;
    // 五连/四/三/二
    if (/11111/.test(s)) score += 100000;
    if (/22222/.test(s)) score -= 100000;
    if (/011110/.test(s)) score += 10000;
    if (/022220/.test(s)) score -= 10000;
    if (/01110/.test(s)) score += 1000;
    if (/02220/.test(s)) score -= 1000;
    if (/001110|011100|011010|010110/.test(s)) score += 500;
    if (/002220|022200|022020|020220/.test(s)) score -= 500;
    if (/00110|01100|01010/.test(s)) score += 100;
    if (/00220|02200|02020/.test(s)) score -= 100;
    return score;
  };

  let total = 0;
  // 横、竖、两斜线扫描
  for (let y=0;y<size;y++){
    total += scoreLines(board[y]);
  }
  for (let x=0;x<size;x++){
    const col = []; for (let y=0;y<size;y++) col.push(board[y][x]);
    total += scoreLines(col);
  }
  for (let k=0;k<=2*(size-1);k++){
    const diag1=[]; const diag2=[];
    for (let y=0;y<size;y++){
      const x1=k-y; const x2=size-1-(k-y);
      if (x1>=0 && x1<size) diag1.push(board[y][x1]);
      if (x2>=0 && x2<size) diag2.push(board[y][x2]);
    }
    if (diag1.length>=5) total += scoreLines(diag1);
    if (diag2.length>=5) total += scoreLines(diag2);
  }
  // 轮到谁走的微调
  return colorToMove===1 ? total : -total;
}
