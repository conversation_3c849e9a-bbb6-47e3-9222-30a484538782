let x, y;
let speedX, speedY;
let r = 25;

function setup() {
  createCanvas(400, 400);
  x = width / 2;
  y = height / 2;
  speedX = random(2, 5);
  speedY = random(2, 5);
}

function draw() {
  background(220);
  
  x += speedX;
  y += speedY;
  
  if (x + r > width || x - r < 0) {
    speedX *= -1;
  }
  
  if (y + r > height || y - r < 0) {
    speedY *= -1;
  }
  
  ellipse(x, y, r * 2, r * 2);
}
