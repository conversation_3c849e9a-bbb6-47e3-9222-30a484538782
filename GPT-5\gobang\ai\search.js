import { evaluateBoard, generateCandidateMoves } from './eval.js';

export function findBestMove(board, size, level, aiColor) {
  // 简化版迭代加深 + alpha-beta，难度决定搜索深度
  const maxDepth = level === 1 ? 1 : level === 2 ? 2 : 3;
  const candidate = generateCandidateMoves(board, size);
  let best = { x: Math.floor(size/2), y: Math.floor(size/2) };
  let bestScore = -Infinity;
  for (const mv of candidate) {
    board[mv.y][mv.x] = aiColor;
    const score = -negamax(board, size, maxDepth - 1, -Infinity, Infinity, 3 - aiColor);
    board[mv.y][mv.x] = 0;
    if (score > bestScore) { bestScore = score; best = mv; }
  }
  return best;
}

function negamax(board, size, depth, alpha, beta, color) {
  const winScore = evaluateWin(board, size);
  if (winScore !== 0) return winScore * (depth + 1);
  if (depth === 0) return evaluateBoard(board, size, color);

  let best = -Infinity;
  const moves = generateCandidateMoves(board, size);
  for (const mv of moves) {
    board[mv.y][mv.x] = color;
    const score = -negamax(board, size, depth - 1, -beta, -alpha, 3 - color);
    board[mv.y][mv.x] = 0;
    if (score > best) best = score;
    if (best > alpha) alpha = best;
    if (alpha >= beta) break;
  }
  return best;
}

function evaluateWin(board, size) {
  // 快速检测是否已有五连
  const dirs = [[1,0],[0,1],[1,1],[1,-1]];
  for (let y=0;y<size;y++){
    for(let x=0;x<size;x++){
      const v = board[y][x]; if (!v) continue;
      for (const [dx,dy] of dirs){
        let c=1; let i=1;
        while (x+dx*i>=0 && y+dy*i>=0 && x+dx*i<size && y+dy*i<size && board[y+dy*i][x+dx*i]===v){ c++; i++; }
        i=1; while (x-dx*i>=0 && y-dy*i>=0 && x-dx*i<size && y-dy*i<size && board[y-dy*i][x-dx*i]===v){ c++; i++; }
        if (c>=5) return v===1? 100000 : -100000;
      }
    }
  }
  return 0;
}
