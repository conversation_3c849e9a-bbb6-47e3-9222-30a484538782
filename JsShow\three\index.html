<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Three.js - 旋转地球</title>
  <style>
    html, body { margin: 0; height: 100%; background: #0b0e14; color: #e6e6e6; font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial; }
    #ui { position: fixed; top: 12px; left: 12px; background: rgba(0,0,0,.35); border: 1px solid rgba(255,255,255,.12); border-radius: 10px; padding: 10px 12px; font-size: 13px; z-index: 1; }
    a { color: #5dd3fb; text-decoration: none; }
    canvas { display: block; }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>

</head>
<body>
  <div id="ui">Three.js 旋转地球 | 拖拽旋转/缩放 | <a href="../index.html">返回</a></div>
  <script>
    // 兜底：如 three.js 未成功加载，给出提示，避免空白页
    if (!window.THREE) {
      document.body.innerHTML = '<div style="padding:24px;color:#e6e6e6;background:#0b0e14;font-family:system-ui">无法加载 three.js（网络或 CDN 受限）。请连接网络或使用本地服务器后刷新。</div>';
    } else {
      let renderer, scene, camera, globe, atmosphere;
      const DPR = Math.min(window.devicePixelRatio || 1, 2);

      function init() {
        renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(DPR);
        renderer.setClearColor(0x0b0e14, 1);
        // 色彩空间设置（r165）
        if (renderer.outputColorSpace !== undefined) {
          renderer.outputColorSpace = THREE.SRGBColorSpace;
        }
        document.body.appendChild(renderer.domElement);

        scene = new THREE.Scene();
        camera = new THREE.PerspectiveCamera(60, window.innerWidth/window.innerHeight, 0.1, 1000);
        camera.position.set(0, 0, 3.2);

        const ambient = new THREE.AmbientLight(0xffffff, 0.7);
        scene.add(ambient);
        const dir = new THREE.DirectionalLight(0xffffff, 1.0);
        dir.position.set(5, 3, 5);
        scene.add(dir);

        const loader = new THREE.TextureLoader();
        if (loader.setCrossOrigin) loader.setCrossOrigin('anonymous');
        // 先尝试本地资源，再退回远程
        function tryLoadTexture(paths, onLoad) {
          const pathList = Array.isArray(paths) ? paths.slice() : [paths];
          function attempt() {
            if (pathList.length === 0) return onLoad(null);
            const p = pathList.shift();
            loader.load(p, tex => onLoad(tex), () => {}, () => { attempt(); });
          }
          attempt();
        }

        let earthTex = null, bumpTex = null, specTex = null;
        tryLoadTexture([
          './assets/earth_atmos_2048.jpg',
          './assets/earth.jpg',
          'https://threejs.org/examples/textures/planets/earth_atmos_2048.jpg'
        ], tex => { earthTex = tex; });
        tryLoadTexture([
          './assets/earth_bump_2048.jpg',
          './assets/earthbump1k.jpg',
          'https://threejs.org/examples/textures/planets/earth_bump_2048.jpg',
          'https://threejs.org/examples/textures/earthbump1k.jpg'
        ], tex => { bumpTex = tex; });
        tryLoadTexture([
          './assets/earth_specular_2048.jpg',
          './assets/earthspec1k.jpg',
          'https://threejs.org/examples/textures/planets/earth_specular_2048.jpg',
          'https://threejs.org/examples/textures/earthspec1k.jpg'
        ], tex => { specTex = tex; });

        const geo = new THREE.SphereGeometry(1, 64, 64);
        const mat = new THREE.MeshPhongMaterial({
          color: 0x88aadd,
          bumpScale: 0.02,
          shininess: 10
        });
        // 纹理异步就绪后赋值
        const assignWhenReady = () => {
          if (earthTex) { mat.map = earthTex; mat.needsUpdate = true; }
          if (bumpTex) { mat.bumpMap = bumpTex; mat.needsUpdate = true; }
          if (specTex) { mat.specularMap = specTex; mat.needsUpdate = true; }
          requestAnimationFrame(assignWhenReady);
        };
        assignWhenReady();
        globe = new THREE.Mesh(geo, mat);
        scene.add(globe);

        const atmoGeo = new THREE.SphereGeometry(1.03, 64, 64);
        const atmoMat = new THREE.MeshBasicMaterial({ color: 0x66ccff, transparent: true, opacity: 0.18, blending: THREE.AdditiveBlending, side: THREE.BackSide });
        atmosphere = new THREE.Mesh(atmoGeo, atmoMat);
        scene.add(atmosphere);

        // 简易拖拽旋转与滚轮缩放（避免 OrbitControls 依赖问题）
        setupInteractions(renderer.domElement);

        window.addEventListener('resize', onResize);
        animate();
      }

      function onResize() {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      }

      // 交互：拖拽旋转 + 滚轮缩放
      let isDragging = false;
      let lastX = 0, lastY = 0;
      let velX = 0, velY = 0; // 惯性
      function setupInteractions(target) {
        target.addEventListener('pointerdown', (e) => {
          isDragging = true;
          lastX = e.clientX; lastY = e.clientY;
          target.setPointerCapture(e.pointerId);
        });
        target.addEventListener('pointermove', (e) => {
          if (!isDragging || !globe) return;
          const dx = e.clientX - lastX;
          const dy = e.clientY - lastY;
          lastX = e.clientX; lastY = e.clientY;
          const rotY = dx * 0.005;
          const rotX = dy * 0.003;
          globe.rotation.y += rotY;
          globe.rotation.x = clamp(globe.rotation.x + rotX, -Math.PI/2, Math.PI/2);
          atmosphere.rotation.y = globe.rotation.y;
          atmosphere.rotation.x = globe.rotation.x;
          velX = rotX; velY = rotY;
        });
        target.addEventListener('pointerup', (e) => {
          isDragging = false;
          try { target.releasePointerCapture(e.pointerId); } catch(_){}
        });
        target.addEventListener('wheel', (e) => {
          e.preventDefault();
          const dz = e.deltaY * 0.0015;
          const z = clamp(camera.position.z + dz, 1.5, 6);
          camera.position.z = z;
        }, { passive: false });
      }

      function clamp(v, min, max) { return Math.max(min, Math.min(max, v)); }

      function animate() {
        requestAnimationFrame(animate);
        // 旋转 + 惯性
        if (globe) {
          globe.rotation.y += 0.0012 + velY;
          globe.rotation.x = clamp(globe.rotation.x + velX, -Math.PI/2, Math.PI/2);
        }
        if (atmosphere) {
          atmosphere.rotation.y = globe.rotation.y;
          atmosphere.rotation.x = globe.rotation.x;
        }
        velX *= 0.94; velY *= 0.94;
        if (renderer && scene && camera) renderer.render(scene, camera);
      }

      try { init(); } catch (e) {
        document.body.innerHTML = '<div style="padding:24px;color:#e6e6e6;background:#0b0e14;font-family:system-ui">渲染初始化失败：' + (e && e.message ? e.message : e) + '</div>';
      }
    }
  </script>
</body>
</html>
