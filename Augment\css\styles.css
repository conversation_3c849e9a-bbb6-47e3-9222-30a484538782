/* 基础样式与配色 */
:root{
  --bg:#0f172a;         /* 深靛蓝 */
  --bg-alt:#111827;     /* 深灰蓝 */
  --card:#0b1220;       /* 卡片底色 */
  --text:#e5e7eb;       /* 主文字 */
  --muted:#9ca3af;      /* 次要文字 */
  --primary:#22d3ee;    /* 青色强调 */
  --accent:#a78bfa;     /* 紫色强调 */
  --ok:#34d399;         /* 正确 */
  --warn:#fbbf24;       /* 警示 */
  --error:#f87171;      /* 错误 */
  --surface:#1f2937;
  --chip:#0ea5e9;
}
*{box-sizing:border-box}
html,body{margin:0;padding:0}
body{
  font-family: 'Noto Sans SC', system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  color:var(--text); background:linear-gradient(120deg,#0b1020,#0f172a 50%,#0b1020);
  line-height:1.65; font-size:16px;
}
.container{max-width:1100px;margin:0 auto;padding:0 16px}

/* 头部 */
.site-header{position:sticky;top:0;z-index:40;background:rgba(17,24,39,.8);backdrop-filter:blur(8px);border-bottom:1px solid rgba(255,255,255,.06)}
.header-inner{display:flex;align-items:center;gap:16px;padding:12px 0}
.brand{font-weight:700;letter-spacing:.5px}
.main-nav{display:flex;gap:12px;flex:1;flex-wrap:wrap}
.main-nav a{color:var(--text);text-decoration:none;padding:8px 10px;border-radius:8px}
.main-nav a:hover{background:rgba(255,255,255,.06)}
.nav-toggle{display:none;background:var(--surface);color:var(--text);border:1px solid rgba(255,255,255,.08);padding:6px 10px;border-radius:8px}

/* 英雄区 */
.hero{padding:60px 0;background:radial-gradient(800px 300px at 20% 20%, rgba(34,211,238,.15), transparent 60%), radial-gradient(800px 300px at 80% 0%, rgba(167,139,250,.18), transparent 60%)}
.hero h1{font-size:28px;margin:.2em 0}
.subtitle{color:var(--muted);max-width:70ch}
.quick-links{margin-top:16px;display:flex;flex-wrap:wrap;gap:10px}
.chip{display:inline-block;background:rgba(14,165,233,.15);border:1px solid rgba(14,165,233,.35);color:#a5f3fc;padding:6px 10px;border-radius:999px;text-decoration:none}

/* 通用区块 */
.section{padding:48px 0}
.section.alt{background:linear-gradient(180deg, rgba(255,255,255,.03), transparent 50%)}
.lead{color:#cbd5e1}
.grid.two{display:grid;grid-template-columns:1fr 1fr;gap:18px}
.card{background:var(--card);border:1px solid rgba(255,255,255,.06);border-radius:14px;padding:16px;box-shadow:0 10px 30px rgba(0,0,0,.25)}
.card h3{margin-top:0}
.muted{color:var(--muted)}

/* 力学演示 */
.demo{margin-top:8px}
.control-row{display:flex;gap:12px;align-items:center;flex-wrap:wrap;margin:10px 0}
.track{position:relative;height:80px;background:linear-gradient(180deg,#0b1323,#0a1120);border-radius:10px;border:1px solid rgba(255,255,255,.06);overflow:hidden}
.car{position:absolute;left:6px;top:18px;width:44px;height:44px;background:linear-gradient(135deg,#22d3ee,#0ea5e9);border-radius:10px;box-shadow:0 6px 18px rgba(34,211,238,.5)}
.ruler{position:absolute;bottom:0;left:0;right:0;height:16px;background:repeating-linear-gradient(90deg, rgba(255,255,255,.2) 0 2px, transparent 2px 20px)}

/* 压强可视化 */
.pressure-viz{display:flex;align-items:center;gap:12px}
.block{width:60px;height:60px;background:linear-gradient(135deg,#818cf8,#6366f1);border-radius:10px}
.gauge{flex:1;height:16px;background:rgba(255,255,255,.06);border-radius:999px;overflow:hidden;border:1px solid rgba(255,255,255,.08)}
.gauge .bar{height:100%;width:30%;background:linear-gradient(90deg,#22d3ee,#a78bfa)}
.gauge.heat{height:12px}
.gauge.heat #qBar{height:100%;width:0;background:linear-gradient(90deg,#34d399,#22d3ee)}

/* 声学示波 */
#sndScope{width:100%;background:linear-gradient(180deg,rgba(255,255,255,.06),transparent);border-radius:10px;border:1px solid rgba(255,255,255,.08)}

/* 光学画布 */
#lightCanvas{width:100%;background:linear-gradient(180deg,#0b1323,#0a1120);border-radius:10px;border:1px solid rgba(255,255,255,.08)}

/* 热学 */
#tempFOut,#qOut{font-weight:700;color:#e2e8f0}

/* 浮沉水槽 */
.float-tank{position:relative;height:200px;border-radius:12px;overflow:hidden;border:1px solid rgba(255,255,255,.08);background:linear-gradient(180deg,#07101c,#0a1524)}
.float-tank .water{position:absolute;left:0;right:0;bottom:0;height:70%;background:linear-gradient(180deg, rgba(56,189,248,.35), rgba(56,189,248,.15))}
.float-tank .object{position:absolute;left:50%;transform:translateX(-50%);bottom:65%;width:48px;height:48px;border-radius:10px;background:linear-gradient(135deg,#f59e0b,#fbbf24);box-shadow:0 10px 25px rgba(251,191,36,.35)}

/* 测验 */
.quiz .q{background:var(--card);border:1px solid rgba(255,255,255,.06);border-radius:12px;padding:12px;margin:12px 0}
.quiz label{display:block;margin:6px 0}
.quiz .feedback{margin-top:6px;min-height:20px}
.quiz .correct{color:var(--ok)}
.quiz .wrong{color:var(--error)}

/* 页脚 */
.site-footer{border-top:1px solid rgba(255,255,255,.06);padding:16px 0;background:rgba(17,24,39,.7)}
.site-footer p{color:var(--muted);margin:6px 0}

/* 响应式 */
@media (max-width: 860px){
  .grid.two{grid-template-columns:1fr}
  .main-nav{display:none}
  .nav-toggle{display:block;margin-left:auto}
  .main-nav.open{display:flex;flex-direction:column;gap:6px;background:rgba(15,23,42,.95);position:absolute;top:56px;left:0;right:0;padding:10px;border-bottom:1px solid rgba(255,255,255,.08)}
}

