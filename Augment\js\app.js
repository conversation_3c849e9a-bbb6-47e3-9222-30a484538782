// 工具函数
const $ = (sel) => document.querySelector(sel);

// 顶部导航：移动端展开
const navToggle = $('#navToggle');
const mainNav = $('#mainNav');
navToggle?.addEventListener('click',()=>{
  mainNav.classList.toggle('open');
});

// 力学：小车匀速运动
(function(){
  const car = $('#mechCar');
  const track = $('#mechTrack');
  const speedInput = $('#mechSpeed');
  const speedVal = $('#mechSpeedVal');
  const btnStart = $('#mechStart');
  const btnPause = $('#mechPause');
  let running=false, last=null, x=6; // 像素位置

  const pxPerMsPerMS = 0.08; // 将物理速度映射为像素/毫秒比例

  function step(ts){
    if(!running){ last = ts; requestAnimationFrame(step); return; }
    if(last==null) last = ts;
    const dt = ts - last; last = ts;
    const v = Number(speedInput.value); // m/s
    const dx = v * pxPerMsPerMS * dt; // 像素
    const maxX = track.clientWidth - car.clientWidth - 6;
    x = Math.min(x + dx, maxX);
    car.style.left = x + 'px';
    if(x >= maxX){ running=false; }
    requestAnimationFrame(step);
  }
  requestAnimationFrame(step);

  speedInput?.addEventListener('input',()=>{
    speedVal.textContent = speedInput.value;
  });
  btnStart?.addEventListener('click',()=>{ running=true; });
  btnPause?.addEventListener('click',()=>{ running=false; });
})();

// 压强：p=F/S
(function(){
  const f = $('#pressF');
  const a = $('#pressA');
  const fVal = $('#pressFVal');
  const aVal = $('#pressAVal');
  const pVal = $('#pressVal');
  const bar = $('#pressBar');

  function update(){
    const F = Number(f.value);
    const S = Number(a.value);
    const p = S>0? F/S : 0; // Pa
    fVal.textContent = F.toFixed(0);
    aVal.textContent = Number(S).toFixed(3);
    pVal.textContent = Math.round(p).toString();
    // 归一化至 0-100%（以 100kPa 为上限）
    const percent = Math.max(0, Math.min(100, (p/100000)*100));
    bar.style.width = percent + '%';
  }
  f?.addEventListener('input',update);
  a?.addEventListener('input',update);
  update();
})();

// 声学：Web Audio + 示波
(function(){
  const freq = $('#sndFreq');
  const freqVal = $('#sndFreqVal');
  const gain = $('#sndGain');
  const gainVal = $('#sndGainVal');
  const toggle = $('#sndToggle');
  const scope = $('#sndScope');
  const ctx2d = scope?.getContext('2d');

  let audioCtx = null, osc = null, gainNode = null, analyser = null, playing=false;

  function ensureAudio(){
    if(audioCtx) return;
    const A = window.AudioContext || window.webkitAudioContext;
    audioCtx = new A();
    osc = audioCtx.createOscillator();
    gainNode = audioCtx.createGain();
    analyser = audioCtx.createAnalyser();
    analyser.fftSize = 1024;
    osc.type = 'sine';
    osc.frequency.value = Number(freq.value);
    gainNode.gain.value = Number(gain.value);
    osc.connect(gainNode).connect(analyser).connect(audioCtx.destination);
    osc.start();
  }

  function draw(){
    if(!ctx2d || !analyser) return;
    const W = scope.width, H = scope.height;
    ctx2d.clearRect(0,0,W,H);
    // 背景网格
    ctx2d.strokeStyle = 'rgba(255,255,255,0.08)';
    ctx2d.lineWidth = 1;
    for(let x=0;x<W;x+=26){ ctx2d.beginPath(); ctx2d.moveTo(x,0); ctx2d.lineTo(x,H); ctx2d.stroke(); }
    for(let y=0;y<H;y+=20){ ctx2d.beginPath(); ctx2d.moveTo(0,y); ctx2d.lineTo(W,y); ctx2d.stroke(); }

    const buffer = new Uint8Array(analyser.fftSize);
    analyser.getByteTimeDomainData(buffer);
    ctx2d.beginPath();
    ctx2d.strokeStyle = '#22d3ee';
    const mid = H/2;
    for(let i=0;i<buffer.length;i++){
      const t = i / (buffer.length-1);
      const x = t * W;
      const y = mid + (buffer[i]-128)/128 * (H*0.4);
      i===0? ctx2d.moveTo(x,y) : ctx2d.lineTo(x,y);
    }
    ctx2d.stroke();
    requestAnimationFrame(draw);
  }

  toggle?.addEventListener('click', async ()=>{
    if(!playing){
      ensureAudio();
      // iOS 需在用户交互后 resume
      await audioCtx.resume?.();
      playing=true; toggle.textContent='停止';
      requestAnimationFrame(draw);
    }else{
      playing=false; toggle.textContent='播放';
      // 将音量拉至 0 实现“静音”（不销毁节点以保留波形）
      if(gainNode) gainNode.gain.value = 0;
    }
  });
  freq?.addEventListener('input',()=>{
    freqVal.textContent = freq.value;
    if(osc) osc.frequency.value = Number(freq.value);
  });
  gain?.addEventListener('input',()=>{
    gainVal.textContent = Number(gain.value).toFixed(2);
    if(gainNode) gainNode.gain.value = Number(gain.value);
  });
})();

// 光学：反射定律绘制
(function(){
  const angleInput = $('#lightAngle');
  const angleVal = $('#lightAngleVal');
  const canvas = $('#lightCanvas');
  const ctx = canvas?.getContext('2d');

  function draw(){
    if(!ctx) return;
    const W = canvas.width, H = canvas.height;
    ctx.clearRect(0,0,W,H);

    // 镜面（水平线）、法线（垂直线）
    const mirrorY = H*0.6; const cx = W*0.5;
    ctx.strokeStyle = 'rgba(255,255,255,0.45)'; ctx.lineWidth=2;
    ctx.beginPath(); ctx.moveTo(30, mirrorY); ctx.lineTo(W-30, mirrorY); ctx.stroke();
    ctx.setLineDash([6,6]); ctx.beginPath(); ctx.moveTo(cx, mirrorY-90); ctx.lineTo(cx, mirrorY+90); ctx.stroke(); ctx.setLineDash([]);

    const theta = Number(angleInput.value) * Math.PI/180; // 入射角，相对法线

    // 入射光线
    const L = 140;
    const ix = cx - Math.sin(theta)*L; const iy = mirrorY - Math.cos(theta)*L;
    ctx.strokeStyle = '#22d3ee'; ctx.lineWidth=3;
    ctx.beginPath(); ctx.moveTo(ix, iy); ctx.lineTo(cx, mirrorY); ctx.stroke();

    // 反射光线（角相等）
    const rx = cx + Math.sin(theta)*L; const ry = mirrorY - Math.cos(theta)*L;
    ctx.strokeStyle = '#a78bfa';
    ctx.beginPath(); ctx.moveTo(cx, mirrorY); ctx.lineTo(rx, ry); ctx.stroke();

    // 角度标注
    ctx.fillStyle = 'rgba(255,255,255,0.8)';
    ctx.font = '14px system-ui, sans-serif';
    ctx.fillText('入射角 = 反射角 = ' + angleInput.value + '°', 20, 26);
  }

  angleInput?.addEventListener('input', ()=>{
    angleVal.textContent = angleInput.value + '°';
    draw();
  });
  draw();
})();

// 热学：温度换算与热量 Q
(function(){
  const tempC = $('#tempC');
  const toF = $('#toF');
  const tempFOut = $('#tempFOut');
  const matC = $('#matC');
  const massC = $('#massC');
  const deltaT = $('#deltaT');
  const calcQ = $('#calcQ');
  const qOut = $('#qOut');
  const qBar = $('#qBar');

  function renderF(){
    const C = Number(tempC.value);
    const F = C*9/5 + 32;
    tempFOut.textContent = F.toFixed(0) + ' °F';
  }
  function renderQ(){
    const c = Number(matC.value); // 比热 J/(kg·℃)
    const m = Number(massC.value);
    const dT = Number(deltaT.value);
    const Q = c*m*dT; // J
    qOut.textContent = 'Q = ' + (Q/1000).toFixed(1) + ' kJ';
    const percent = Math.max(0, Math.min(100, (Q/200000)*100)); // 以 200kJ 为满
    qBar.style.width = percent + '%';
  }

  toF?.addEventListener('click', renderF);
  calcQ?.addEventListener('click', renderQ);
  renderF(); renderQ();
})();

// 密度与浮沉
(function(){
  const m = $('#densM');
  const V = $('#densV');
  const rhoOut = $('#rhoOut');
  const obj = $('#floatObj');

  function update(){
    const mass = Number(m.value);
    const vol = Number(V.value);
    const rho = vol>0? mass/vol : 0; // kg/m^3
    rhoOut.textContent = 'ρ = ' + Math.round(rho) + ' kg/m³';
    // 判定浮沉
    if(rho < 1000){
      obj.style.bottom = '75%'; // 浮
    }else if(Math.abs(rho-1000) < 30){
      obj.style.bottom = '35%'; // 悬
    }else{
      obj.style.bottom = '5%'; // 沉
    }
  }
  m?.addEventListener('input', update);
  V?.addEventListener('input', update);
  $('#calcRho')?.addEventListener('click', update);
  update();
})();

// 小测验：即时反馈
(function(){
  document.querySelectorAll('.quiz .q').forEach((q, idx)=>{
    const answer = q.getAttribute('data-answer');
    const fb = q.querySelector('.feedback');
    q.querySelectorAll('input[type="radio"]').forEach(r=>{
      r.addEventListener('change', ()=>{
        const val = r.value;
        if(val === answer){
          fb.textContent = '✔ 回答正确！';
          fb.className = 'feedback correct';
        }else{
          fb.textContent = '✖ 再想一想～';
          fb.className = 'feedback wrong';
        }
      });
    });
  });
})();

