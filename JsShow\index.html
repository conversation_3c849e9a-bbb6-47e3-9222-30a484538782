<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>JsShow - 经典演示合集</title>
  <style>
    :root { --bg: #0b0e14; --card: #121826; --text: #e6e6e6; --muted: #9aa4b2; --brand: #5dd3fb; }
    * { box-sizing: border-box; }
    html, body { height: 100%; }
    body {
      margin: 0; font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial;
      background: radial-gradient(1200px 800px at 15% -10%, #132038 0%, transparent 60%),
                  radial-gradient(1000px 700px at 110% 10%, #0f2a2e 0%, transparent 60%),
                  var(--bg); color: var(--text);
    }
    header { padding: 36px 24px 0; text-align: center; }
    h1 { margin: 0; font-size: 28px; letter-spacing: .3px; }
    p.lead { margin: 8px 0 24px; color: var(--muted); }
    main { max-width: 1080px; margin: 0 auto; padding: 24px; }
    .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 16px; }
    a.card { display: block; padding: 18px; border-radius: 14px; background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02));
      border: 1px solid rgba(255,255,255,.08); text-decoration: none; color: inherit; transition: transform .18s ease, border-color .18s ease; }
    a.card:hover { transform: translateY(-4px); border-color: rgba(93,211,251,.6); }
    .card h3 { margin: 0 0 8px; font-size: 18px; }
    .card p { margin: 0; color: var(--muted); line-height: 1.45; font-size: 14px; }
    footer { text-align: center; color: var(--muted); padding: 28px 0 40px; font-size: 13px; }
  </style>
</head>
<body>
  <header>
    <h1>JsShow - 经典演示合集</h1>
    <p class="lead">使用 p5.js、Three.js、D3.js 分别实现一个经典可视化/交互示例。</p>
  </header>

  <main>
    <div class="grid">
      <a class="card" href="./p5/index.html">
        <h3>p5.js - 交互粒子流</h3>
        <p>经典鼠标吸引粒子，流体般动效与噪声运动。</p>
      </a>

      <a class="card" href="./three/index.html">
        <h3>Three.js - 旋转地球</h3>
        <p>纹理贴图、光照与轨道控制，最常见的 3D 入门示例。</p>
      </a>

      <a class="card" href="./d3/index.html">
        <h3>D3.js - 过渡柱状图</h3>
        <p>数据更新与过渡动画，展示 D3 的数据-视图绑定威力。</p>
      </a>
    </div>
  </main>

  <footer>打开任意卡片预览对应示例。建议使用本地静态服务器访问。</footer>
</body>
</html>
