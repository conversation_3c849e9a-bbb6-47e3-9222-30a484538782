:root{--bg:#0a0f14;--panel:#0f141a;--grid:rgba(255,255,255,.08);--text:#e6edf3;--muted:#a0a8b3;--brand:#89b4ff;--accent:#86efac;--ring:rgba(134,239,172,.35);--board-size:640px;--board-pad:12px}
*{box-sizing:border-box}
html,body{height:100%}
body{margin:0;background:radial-gradient(1200px 800px at 80% -10%, rgba(134,239,172,0.10), transparent),radial-gradient(900px 600px at 10% 0%, rgba(137,180,255,0.10), transparent),var(--bg);color:var(--text);font-family:'Inter', system-ui, -apple-system, Segoe UI, Roboto, PingFang SC, Noto Sans SC, Helvetica, Arial, sans-serif}
button,select,input{font:inherit;color:inherit}
select,input,button{outline:none}

.app{max-width:980px;margin:0 auto;padding:32px 20px}
.topbar{display:flex;justify-content:center;align-items:center;padding:6px 0}
.brand{display:flex;align-items:center;gap:10px;font-weight:800;letter-spacing:.3px}
.spark{width:10px;height:10px;background:linear-gradient(135deg,var(--accent),var(--brand));border-radius:3px;box-shadow:0 0 0 6px var(--ring),0 0 24px var(--accent)}

.main-centered{display:flex;justify-content:center;margin-top:16px}
.board-wrap{position:relative;border-radius:20px;padding:var(--board-pad);border:1px solid var(--grid);background:linear-gradient(180deg,rgba(255,255,255,.02),rgba(255,255,255,0));width:calc(var(--board-size) + var(--board-pad)*2)}
.board-zen{box-shadow:inset 0 0 0 1px rgba(255,255,255,.04),0 20px 60px rgba(0,0,0,.35)}
.overlay-info{position:absolute;top:12px;left:12px;padding:6px 10px;border-radius:12px;border:1px solid var(--grid);color:var(--muted);background:rgba(0,0,0,.24);backdrop-filter:blur(8px);font-size:12px}

.dock{position:sticky;bottom:12px;margin:16px auto 0;display:flex;justify-content:space-between;align-items:center;gap:12px;padding:10px 12px;border:1px solid var(--grid);border-radius:14px;background:rgba(15,20,26,.6);backdrop-filter:blur(8px);width:calc(var(--board-size) + var(--board-pad)*2)}
.dock-left,.dock-right{display:flex;align-items:center;gap:8px;flex-wrap:wrap}
.btn{padding:10px 14px;border-radius:12px;border:1px solid var(--grid);background:linear-gradient(180deg,rgba(134,239,172,.12),rgba(134,239,172,.04));cursor:pointer}
.btn:hover{transform:translateY(-1px);box-shadow:0 8px 24px rgba(0,0,0,.25);border-color:var(--accent)}
.dock-seg{display:flex;align-items:center;gap:8px}
.dock-seg select{background-color:#0f1620;border:1px solid var(--grid);border-radius:10px;padding:8px 10px;color:var(--text);appearance:none;-webkit-appearance:none;-moz-appearance:none;color-scheme:dark}
.dock-seg option{background:#0f1620;color:var(--text)}
.dock-check{display:flex;align-items:center;gap:6px;color:var(--muted)}

.footer{margin-top:20px;color:var(--muted);text-align:center;font-size:12px}

@media(max-width:720px){
  .app{padding:20px 12px}
  .dock{position:static}
}