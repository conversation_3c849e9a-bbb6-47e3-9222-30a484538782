<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>初二上册物理要点 | 交互式学习</title>
  <meta name="description" content="中国八年级（初二）上册物理核心知识：力学、声学、光学、热学、物质与密度。含交互演示、练习题与可视化。" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="css/styles.css" />
</head>
<body>
  <header class="site-header">
    <div class="container header-inner">
      <div class="brand">初二物理 · 上册</div>
      <nav class="main-nav" id="mainNav">
        <a href="#intro">首页</a>
        <a href="#mechanics">力学</a>
        <a href="#sound">声学</a>
        <a href="#light">光学</a>
        <a href="#heat">热与温度</a>
        <a href="#matter">物质与密度</a>
        <a href="#quiz">小测验</a>
      </nav>
      <button class="nav-toggle" id="navToggle" aria-label="切换导航">☰</button>
    </div>
  </header>

  <main>
    <section id="intro" class="section hero">
      <div class="container">
        <h1>八年级上册物理核心知识（交互式）</h1>
        <p class="subtitle">依据中国初中物理课程标准整理，包含：力学（运动、力、压强）、声学、光学、热学、物质与密度。配有简明讲解、可视化与小练习。</p>
        <div class="quick-links">
          <a class="chip" href="#mechanics">力学</a>
          <a class="chip" href="#sound">声学</a>
          <a class="chip" href="#light">光学</a>
          <a class="chip" href="#heat">热与温度</a>
          <a class="chip" href="#matter">物质与密度</a>
        </div>
      </div>
    </section>

    <section id="mechanics" class="section">
      <div class="container">
        <h2>力学 Mechanics</h2>
        <p class="lead">描述机械运动（参照物、速度）、常见的力（重力、弹力、摩擦力）与压强的基本概念和计算。</p>
        <div class="grid two">
          <article class="card">
            <h3>1) 运动的描述</h3>
            <ul>
              <li><strong>参照物</strong>：描述运动或静止的依据。相对于参照物位置改变的物体在<em>运动</em>，否则<em>静止</em>。</li>
              <li><strong>速度 v</strong>：表示物体运动快慢，v = s / t。常用单位 m/s 或 km/h，1 m/s = 3.6 km/h。</li>
              <li><strong>匀速直线运动</strong>：速度大小和方向都不变的直线运动。</li>
            </ul>
            <div class="demo">
              <div class="control-row">
                <label>速度 v (m/s)：<input id="mechSpeed" type="range" min="0" max="20" step="1" value="6" /></label>
                <span id="mechSpeedVal">6</span>
                <button id="mechStart">开始</button>
                <button id="mechPause">暂停</button>
              </div>
              <div class="track" id="mechTrack" aria-label="运动演示">
                <div class="car" id="mechCar" title="小车"></div>
                <div class="ruler"></div>
              </div>
              <p class="muted">位移 s = v × t。试着改变速度，观察同样时间内位移的变化。</p>
            </div>
          </article>

          <article class="card">
            <h3>2) 力与压强</h3>
            <ul>
              <li><strong>力</strong>：物体间的相互作用，效果有改变形状或改变运动状态，单位牛顿（N）。</li>
              <li><strong>重力 G</strong>≈ m·g（g≈9.8 N/kg）。<strong>弹力</strong>来自形变物体的弹性作用。<strong>摩擦力</strong>与接触面粗糙程度、正压力大小有关。</li>
              <li><strong>压强 p</strong>：单位面积上受到的压力，p = F / S，单位帕斯卡（Pa）。</li>
            </ul>
            <div class="demo">
              <div class="control-row">
                <label>压力 F (N)：<input id="pressF" type="range" min="10" max="1000" step="10" value="200" /></label>
                <span id="pressFVal">200</span>
              </div>
              <div class="control-row">
                <label>受力面积 S (m²)：<input id="pressA" type="range" min="0.001" max="0.1" step="0.001" value="0.02" /></label>
                <span id="pressAVal">0.020</span>
              </div>
              <div class="pressure-viz">
                <div class="block"></div>
                <div class="gauge"><div class="bar" id="pressBar"></div></div>
              </div>
              <p><strong>压强 p = F / S = <span id="pressVal">10000</span> Pa</strong></p>
              <p class="muted">当受力面积减小时，压强增大；如刀口越锋利（面积更小），越容易切开物体。</p>
            </div>
          </article>
        </div>
      </div>
    </section>

    <section id="sound" class="section alt">
      <div class="container">
        <h2>声学 Sound</h2>
        <p class="lead">声音由物体的振动产生，介质传播，真空不能传播声音。声音的三个特征：<strong>响度</strong>（音量）、<strong>音调</strong>（高低，取决于频率）、<strong>音色</strong>（由波形决定）。</p>
        <div class="grid two">
          <article class="card">
            <h3>声的发生与特性</h3>
            <ul>
              <li><strong>声速</strong>：空气中约 340 m/s，液体和固体中更快。</li>
              <li><strong>分贝 dB</strong>：常用于表示响度，保护听力要控制噪声。</li>
              <li><strong>防噪声</strong>：声源处减弱、传播过程阻隔、在接受处防护。</li>
            </ul>
          </article>
          <article class="card">
            <h3>交互：合成器与波形</h3>
            <div class="control-row">
              <label>频率 f (Hz)：<input id="sndFreq" type="range" min="100" max="1000" step="1" value="440" /></label>
              <span id="sndFreqVal">440</span>
            </div>
            <div class="control-row">
              <label>音量 (0-1)：<input id="sndGain" type="range" min="0" max="1" step="0.01" value="0.2" /></label>
              <span id="sndGainVal">0.20</span>
              <button id="sndToggle">播放</button>
            </div>
            <canvas id="sndScope" width="520" height="120" aria-label="波形示波"></canvas>
            <p class="muted">提高频率，音调升高；增大增益，响度增大。示波图展示波形（音色）。</p>
          </article>
        </div>
      </div>
    </section>

    <section id="light" class="section">
      <div class="container">
        <h2>光学 Light</h2>
        <p class="lead">光的直线传播、反射与折射是基础。<strong>反射定律</strong>：入射角等于反射角，入射光线、法线和反射光线在同一平面内。</p>
        <div class="grid two">
          <article class="card">
            <h3>平面镜成像要点</h3>
            <ul>
              <li>像与物等大，成<em>虚像</em>；像与物到镜面的距离相等。</li>
              <li>光的直线传播可用光路图表示。</li>
              <li>入射角、反射角的测量基于法线。</li>
            </ul>
          </article>
          <article class="card">
            <h3>交互：反射定律</h3>
            <div class="control-row">
              <label>入射角 θ (°)：<input id="lightAngle" type="range" min="0" max="80" step="1" value="30" /></label>
              <span id="lightAngleVal">30°</span>
            </div>
            <canvas id="lightCanvas" width="520" height="220" aria-label="反射演示"></canvas>
            <p class="muted">可见入射角与反射角始终相等，且三线共面。</p>
          </article>
        </div>
      </div>
    </section>

    <section id="heat" class="section alt">
      <div class="container">
        <h2>热与温度 Heat</h2>
        <p class="lead">温度表示物体冷热程度；热量是能量转移的多少。热传递方式有<strong>传导、对流、辐射</strong>。常用公式：Q = c·m·ΔT。</p>
        <div class="grid two">
          <article class="card">
            <h3>温度换算与比热容</h3>
            <div class="control-row">
              <label>摄氏温度 ℃：<input id="tempC" type="number" value="25" /></label>
              <button id="toF">转为华氏</button>
              <span id="tempFOut">77 °F</span>
            </div>
            <div class="control-row">
              <label>材料：
                <select id="matC">
                  <option value="4200">水 c=4.2×10³ J/(kg·℃)</option>
                  <option value="900">铝 c=0.90×10³</option>
                  <option value="460">铁 c=0.46×10³</option>
                </select>
              </label>
            </div>
            <div class="control-row">
              <label>质量 m (kg)：<input id="massC" type="number" value="1" step="0.1" /></label>
              <label>升温 ΔT (℃)：<input id="deltaT" type="number" value="10" step="1" /></label>
              <button id="calcQ">计算 Q</button>
              <span id="qOut">Q = 42.0 kJ</span>
            </div>
            <div class="gauge heat"><div id="qBar"></div></div>
            <p class="muted">相同的升温，<em>比热容 c</em>越大，需要的热量越多。</p>
          </article>
          <article class="card">
            <h3>热传递要点</h3>
            <ul>
              <li><strong>传导</strong>：固体中热量由高温向低温传递，如金属导热快。</li>
              <li><strong>对流</strong>：流体受热膨胀密度减小上升，形成循环，如“上冷下热”的室内空气流动。</li>
              <li><strong>辐射</strong>：不需介质即可传热，如太阳光照地球。</li>
            </ul>
          </article>
        </div>
      </div>
    </section>

    <section id="matter" class="section">
      <div class="container">
        <h2>物质与密度 Matter & Density</h2>
        <p class="lead">物体质量 m、体积 V 与密度 ρ 的关系：ρ = m / V。常用单位：kg、m³、g/cm³（1 g/cm³ = 1000 kg/m³）。</p>
        <div class="grid two">
          <article class="card">
            <h3>密度计算与浮沉</h3>
            <div class="control-row">
              <label>质量 m (kg)：<input id="densM" type="number" value="0.5" step="0.1" /></label>
              <label>体积 V (m³)：<input id="densV" type="number" value="0.0005" step="0.0001" /></label>
              <button id="calcRho">计算 ρ</button>
              <span id="rhoOut">ρ = 1000 kg/m³</span>
            </div>
            <div class="float-tank" aria-label="浮沉示意">
              <div class="water"></div>
              <div class="object" id="floatObj"></div>
            </div>
            <p class="muted"><strong>判断浮沉</strong>：ρ物 < ρ水 则上浮；≈相等 悬浮；> ρ水 下沉（ρ水≈1000 kg/m³）。</p>
          </article>
          <article class="card">
            <h3>测量与误差</h3>
            <ul>
              <li>常用测量：刻度尺（读数到毫米）、量筒（读数视线水平，读下凹液面的最低处）。</li>
              <li>误差来源：仪器分度值、读数方法、环境等；多次测量取平均可减小偶然误差。</li>
              <li>有效数字：根据仪器分度值与数据精度合理保留。</li>
            </ul>
          </article>
        </div>
      </div>
    </section>

    <section id="quiz" class="section alt">
      <div class="container">
        <h2>小测验 Quiz</h2>
        <p class="lead">每题只有一个正确选项，作答后立即反馈。</p>
        <div class="quiz">
          <div class="q" data-answer="b">
            <div class="stem">1. 物体相对于所选参照物位置改变，表示该物体：</div>
            <label><input type="radio" name="q1" value="a"> 静止</label>
            <label><input type="radio" name="q1" value="b"> 运动</label>
            <label><input type="radio" name="q1" value="c"> 加速</label>
            <div class="feedback"></div>
          </div>
          <div class="q" data-answer="a">
            <div class="stem">2. 在 F 不变时，减小受力面积 S，压强 p 将：</div>
            <label><input type="radio" name="q2" value="a"> 变大</label>
            <label><input type="radio" name="q2" value="b"> 变小</label>
            <label><input type="radio" name="q2" value="c"> 不变</label>
            <div class="feedback"></div>
          </div>
          <div class="q" data-answer="c">
            <div class="stem">3. 下列不需要介质也能发生的热传递是：</div>
            <label><input type="radio" name="q3" value="a"> 传导</label>
            <label><input type="radio" name="q3" value="b"> 对流</label>
            <label><input type="radio" name="q3" value="c"> 辐射</label>
            <div class="feedback"></div>
          </div>
          <div class="q" data-answer="b">
            <div class="stem">4. 提高声音的频率主要会使：</div>
            <label><input type="radio" name="q4" value="a"> 响度增大</label>
            <label><input type="radio" name="q4" value="b"> 音调升高</label>
            <label><input type="radio" name="q4" value="c"> 音色改变</label>
            <div class="feedback"></div>
          </div>
          <div class="q" data-answer="a">
            <div class="stem">5. 物体密度小于水时，在水中将：</div>
            <label><input type="radio" name="q5" value="a"> 上浮</label>
            <label><input type="radio" name="q5" value="b"> 悬浮</label>
            <label><input type="radio" name="q5" value="c"> 下沉</label>
            <div class="feedback"></div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container">
      <p>© 2025 初二上册物理学习页 · 为教学与自学设计 · 支持手机与桌面浏览</p>
    </div>
  </footer>

  <script src="js/app.js"></script>
</body>
</html>

